// Extract room count from message content
extractRoomCount(content) {
    if (!content) return null;

    // Look for patterns like "# 6 ROOMS #", "# 8 ROOMS", "8 rooms", etc.
    const patterns = [
        /#\s*(\d+)\s*rooms?\s*#/i,  // # 6 ROOMS #
        /#\s*(\d+)\s*rooms?/i,      // # 8 ROOMS
        /(\d+)\s*rooms?\s*#/i,      // 6 ROOMS #
        /(?:#\s*)?(\d+)\s*rooms?/i, // 8 ROOMS or # 8 ROOMS
        /rooms?\s*:?\s*(\d+)/i,     // rooms: 4
        /(\d+)\s*room/i             // 4 room
    ];

    for (const pattern of patterns) {
        const match = content.match(pattern);
        if (match) {
            const roomCount = parseInt(match[1]);
            if (roomCount > 0 && roomCount <= 20) { // Reasonable room count range
                console.log(`[DungeonAlert] Extracted room count: ${roomCount} from message: "${content}"`);
                return roomCount;
            }
        }
    }

    return null;
}

  // Update the dungeon message with room information
  async updateDungeonMessageWithRooms(dungeonData, roomCount) {
    try {
        const channel = await this.client.channels.fetch(dungeonData.channelId);
        if (!channel) {
            console.error('[DungeonAlert] Channel not found for room update');
            return;
        }

        const message = await channel.messages.fetch(dungeonData.messageId);
        if (!message) {
            console.error('[DungeonAlert] Message not found for room update');
            return;
        }

        // Create updated dungeon info with room count
        const updatedDungeonInfo = {
            ...dungeonData.dungeonInfo,
            rooms: roomCount
        };

        // Create new image with room information
        const newAttachment = await this.createDungeonImage(updatedDungeonInfo);
        const rankColor = await this.getRankColor(updatedDungeonInfo.rank);

        // Create updated embed
        const updatedEmbed = new EmbedBuilder()
            .setTitle(dungeonData.title)
            .setColor(rankColor)
            .setImage('attachment://dungeon-info.png')
            .setFooter({ text: 'Beta Access | Public Soon!' });

        // Edit the message with updated image
        await message.edit({
            embeds: [updatedEmbed],
            files: [newAttachment]
        });

        console.log(`[DungeonAlert] ✅ Updated dungeon message ${dungeonData.messageId} with ${roomCount} rooms`);

        // Update stored data
        dungeonData.dungeonInfo.rooms = roomCount;

    } catch (error) {
        console.error('[DungeonAlert] Error updating dungeon message with rooms:', error);
    }
}

// Get current dungeon cycle based on time (xx:15, xx:30, xx:45, xx:00)
getCurrentDungeonCycle() {
    const now = new Date();
    const kolkataTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
    const hour = kolkataTime.getHours();
    const minute = kolkataTime.getMinutes();

    // Determine which 15-minute cycle we're in
    let cycleMinute;
    if (minute >= 0 && minute < 15) cycleMinute = 0;
    else if (minute >= 15 && minute < 30) cycleMinute = 15;
    else if (minute >= 30 && minute < 45) cycleMinute = 30;
    else cycleMinute = 45;

    return `${hour}:${cycleMinute.toString().padStart(2, '0')}`;
}
}

module.exports = DungeonAlert;