const { SlashCommandSubcommandBuilder, EmbedBuilder } = require('discord.js');
const https = require('https');
const config = require('../../../../config/config.js');

module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('user')
        .setDescription('Get Roblox user information')
        .addStringOption(option =>
            option.setName('username')
                .setDescription('Roblox username to lookup')
                .setRequired(false)
        )
        .addUserOption(option =>
            option.setName('discorduser')
                .setDescription('Discord user to get Roblox info for')
                .setRequired(false)
        ),
    name: 'user',
    category: 'Misc',

    async execute(interaction) {
        const username = interaction.options.getString('username');
        const discordUser = interaction.options.getUser('discorduser');

        // Check if at least one option is provided
        if (!username && !discordUser) {
            return await interaction.reply({
                content: '❌ Please provide either a Roblox username or a Discord user!',
                ephemeral: true
            });
        }

        // Check if both options are provided
        if (username && discordUser) {
            return await interaction.reply({
                content: '❌ Please provide either a Roblox username OR a Discord user, not both!',
                ephemeral: true
            });
        }

        await interaction.deferReply();

        try {
            let robloxUsername;
            let avatarUrl = null;

            if (discordUser) {
                // Show searching message
                await interaction.editReply({
                    content: '🔍 Searching user\'s roblox...'
                });

                // Get Roblox username from Discord user via Bloxlink
                const bloxlinkResult = await this.getRobloxUsernameFromDiscord(discordUser);
                if (!bloxlinkResult) {
                    return await interaction.editReply({
                        content: `❌ Could not find Roblox account linked to ${discordUser.tag}. Make sure they have linked their account with Bloxlink.`
                    });
                }

                // Extract username and avatar URL from Bloxlink result
                if (typeof bloxlinkResult === 'object' && bloxlinkResult.username) {
                    robloxUsername = bloxlinkResult.username;
                    avatarUrl = bloxlinkResult.avatarUrl;
                } else {
                    robloxUsername = bloxlinkResult; // Fallback for string result
                    avatarUrl = null;
                }
            } else {
                robloxUsername = username;
                avatarUrl = null;
            }

            console.log(`📤 Looking up Roblox user: ${robloxUsername}`);

            // Get Roblox user information
            const userInfo = await this.getRobloxUserInfo(robloxUsername);
            if (!userInfo) {
                return await interaction.editReply({
                    content: `❌ Could not find Roblox user: ${robloxUsername}`
                });
            }

            // Create and send embed with user information
            const embed = await this.createUserEmbed(userInfo, discordUser, avatarUrl);
            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in roblox user command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while fetching user information. Please try again later.'
            });
        }
    },

    async getRobloxUsernameFromDiscord(discordUser) {
        return new Promise(async (resolve) => {
            try {
                console.log(`🔍 Searching for Roblox username for Discord user: ${discordUser.tag}`);

                // Use the channel ID from config
                const channelId = config.RobloxConfig.bloxlinkChannelId;

                // Use the same approach as discord.js-selfbot-v13
                console.log(`📤 Searching for Bloxlink commands using application-command-index...`);

                // First, search for commands using the same method as discord.js-selfbot-v13
                let commandData;
                let channelInfo;
                try {
                    // Try guild-based search first
                    channelInfo = await this.makeDiscordRequest(`channels/${channelId}`);
                    if (channelInfo.guild_id) {
                        console.log(`📤 Searching in guild ${channelInfo.guild_id}...`);
                        commandData = await this.makeDiscordRequest(`guilds/${channelInfo.guild_id}/application-command-index`);
                    } else {
                        console.log(`📤 Searching in DM channel...`);
                        commandData = await this.makeDiscordRequest(`channels/${channelId}/application-command-index`);
                    }
                } catch (error) {
                    console.log(`❌ Command search failed: ${error.message}`);
                    throw new Error('Failed to search for Bloxlink commands');
                }

                // Find Bloxlink application
                const bloxlinkApp = commandData.applications?.find(app =>
                    app.id === config.RobloxConfig.bloxlinkBotId || app.bot_id === config.RobloxConfig.bloxlinkBotId
                );

                if (!bloxlinkApp) {
                    throw new Error('Bloxlink application not found in this channel/guild');
                }

                // Find getinfo command
                const getinfoCommand = commandData.application_commands?.find(cmd =>
                    cmd.name === 'getinfo' && cmd.application_id === bloxlinkApp.id
                );

                if (!getinfoCommand) {
                    throw new Error('Bloxlink getinfo command not found');
                }

                console.log(`✅ Found Bloxlink getinfo command: ${getinfoCommand.id} (version: ${getinfoCommand.version})`);

                // Create the interaction data using discord.js-selfbot-v13 format
                const nonce = this.generateNonce();
                const guildId = channelInfo.guild_id || null;

                const interactionData = {
                    type: 2, // APPLICATION_COMMAND
                    application_id: bloxlinkApp.id,
                    guild_id: guildId,
                    channel_id: channelId,
                    session_id: this.generateSessionId(),
                    data: {
                        version: getinfoCommand.version,
                        id: getinfoCommand.id,
                        name: getinfoCommand.name,
                        type: getinfoCommand.type,
                        options: [
                            {
                                type: 6, // USER type
                                name: "discord_user",
                                value: discordUser.id
                            }
                        ],
                        application_command: getinfoCommand,
                        attachments: []
                    },
                    nonce: nonce
                };

                console.log(`📤 Interaction data for user ${discordUser.id}:`, JSON.stringify(interactionData, null, 2));

                console.log(`📤 Triggering Bloxlink slash command for user ${discordUser.id}`);

                // Try both approaches - first regular JSON, then multipart if needed
                let commandSent = false;
                try {
                    // First try regular JSON (simpler approach)
                    await this.makeDiscordRequest(`interactions`, {
                        method: 'POST',
                        headers: {
                            'Authorization': config.tokenDungeon,
                            'Content-Type': 'application/json',
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        },
                        body: interactionData
                    });
                    console.log(`✅ Bloxlink slash command sent successfully (JSON)`);
                    commandSent = true;
                } catch (error) {
                    console.log(`❌ JSON approach failed: ${error.message}`);

                    // Check if it's just a response parsing issue but command was sent
                    if (error.message.includes('Unexpected end of JSON input') || error.message.includes('parse')) {
                        console.log(`✅ Command likely sent successfully, just response parsing failed`);
                        commandSent = true;
                    } else {
                        console.log(`📤 Trying multipart approach...`);

                        try {
                            // Fallback to multipart approach
                            await this.makeDiscordRequest(`interactions`, {
                                method: 'POST',
                                headers: {
                                    'Authorization': config.tokenDungeon,
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                                },
                                body: interactionData,
                                usePayloadJSON: true
                            });
                            console.log(`✅ Bloxlink slash command sent successfully (multipart)`);
                            commandSent = true;
                        } catch (multipartError) {
                            console.log(`❌ Multipart approach also failed: ${multipartError.message}`);
                            // Check if it's just response parsing but command was sent
                            if (multipartError.message.includes('CONTENT_TYPE_INVALID') || multipartError.message.includes('parse')) {
                                console.log(`✅ Command likely sent successfully, just response parsing failed`);
                                commandSent = true;
                            }
                        }
                    }
                }

                if (!commandSent) {
                    throw new Error('Failed to send Bloxlink command with both approaches');
                }

                console.log(`✅ Bloxlink slash command interaction sent successfully`);

                console.log(`⏳ Waiting for Bloxlink response...`);

                // Wait for Bloxlink response and parse it
                setTimeout(async () => {
                    try {
                        const messages = await this.makeDiscordRequest(`channels/${channelId}/messages?limit=20`);
                        console.log(`📥 Checking ${messages.length} recent messages for Bloxlink response...`);

                        // Look for Bloxlink response
                        for (const message of messages) {
                            if (message.author.id === config.RobloxConfig.bloxlinkBotId) {
                                console.log(`📋 Found Bloxlink message:`, {
                                    content: message.content,
                                    embeds: message.embeds?.length || 0,
                                    timestamp: message.timestamp
                                });

                                // Check message content for "couldn't find" or error messages
                                if (message.content && (message.content.includes("couldn't find") || message.content.includes("not found"))) {
                                    console.log(`❌ Bloxlink couldn't find user: ${message.content}`);
                                    return resolve(null);
                                }

                                // Check for Bloxlink's special components structure (this is the correct way)
                                if (message.components && message.components.length > 0) {
                                    console.log(`📋 Found ${message.components.length} components in Bloxlink message`);

                                    let foundUsername = null;
                                    let avatarUrl = null;

                                    // Parse through components to find Roblox username and avatar
                                    for (const componentGroup of message.components) {
                                        if (componentGroup.components) {
                                            for (const component of componentGroup.components) {
                                                // Check direct component content
                                                if (component.content && component.content.includes('@')) {
                                                    const match = component.content.match(/###\s*@(\w+)/);
                                                    if (match) {
                                                        foundUsername = match[1];
                                                        console.log(`✅ Found Roblox username in component: ${foundUsername}`);
                                                    }
                                                }

                                                // Check for avatar in accessory
                                                if (component.accessory && component.accessory.media && component.accessory.media.url) {
                                                    // Check if this is a Roblox avatar (not Discord avatar)
                                                    if (component.accessory.media.url.includes('rbxcdn.com')) {
                                                        avatarUrl = component.accessory.media.url;
                                                        console.log(`✅ Found Roblox avatar URL: ${avatarUrl}`);
                                                    }
                                                }

                                                // Check nested components
                                                if (component.components) {
                                                    for (const nestedComponent of component.components) {
                                                        if (nestedComponent.content && nestedComponent.content.includes('@')) {
                                                            const nestedMatch = nestedComponent.content.match(/###\s*@(\w+)/);
                                                            if (nestedMatch) {
                                                                foundUsername = nestedMatch[1];
                                                                console.log(`✅ Found Roblox username in nested component: ${foundUsername}`);
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    if (foundUsername) {
                                        // Store avatar URL for later use in embed
                                        if (avatarUrl) {
                                            console.log(`✅ Using Bloxlink's avatar URL for embed`);
                                        }
                                        return resolve({ username: foundUsername, avatarUrl: avatarUrl });
                                    }
                                }

                                // Fallback: Check traditional embeds if components don't work
                                if (message.embeds && message.embeds.length > 0) {
                                    const embed = message.embeds[0];
                                    console.log(`📋 Embed details:`, {
                                        title: embed.title,
                                        description: embed.description?.substring(0, 100),
                                        fields: embed.fields?.length || 0
                                    });

                                    // Check embed description for username patterns
                                    if (embed.description) {
                                        const descMatch = embed.description.match(/@(\w+)/);
                                        if (descMatch) {
                                            console.log(`✅ Found Roblox username in description: ${descMatch[1]}`);
                                            return resolve({ username: descMatch[1], avatarUrl: null });
                                        }
                                    }

                                    // Check embed fields for Roblox username
                                    if (embed.fields) {
                                        for (const field of embed.fields) {
                                            if (field.value && field.value.includes('@')) {
                                                const fieldMatch = field.value.match(/@(\w+)/);
                                                if (fieldMatch) {
                                                    console.log(`✅ Found Roblox username in field: ${fieldMatch[1]}`);
                                                    return resolve({ username: fieldMatch[1], avatarUrl: null });
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        console.log('❌ No Bloxlink response found with Roblox username');
                        resolve(null);
                    } catch (error) {
                        console.error('Error parsing Bloxlink response:', error);
                        resolve(null);
                    }
                }, 5000); // Wait 5 seconds for Bloxlink to respond

            } catch (error) {
                console.error('Error getting Roblox username from Discord:', error);
                resolve(null);
            }
        });
    },

    async getRobloxUserInfo(username) {
        try {
            // First, get user ID from username
            const userSearchResponse = await this.makeRobloxRequest(`users.roblox.com`, `/v1/users/search?keyword=${encodeURIComponent(username)}&limit=10`);

            if (!userSearchResponse.data || userSearchResponse.data.length === 0) {
                return null;
            }

            const userId = userSearchResponse.data[0].id;

            // Get detailed user information
            const userInfoResponse = await this.makeRobloxRequest(`users.roblox.com`, `/v1/users/${userId}`);

            return userInfoResponse;
        } catch (error) {
            console.error('Error fetching Roblox user info:', error);
            return null;
        }
    },

    async createUserEmbed(userInfo, discordUser, avatarUrl = null) {
        // Get high-quality avatar from Roblox API or use provided avatar
        let avatarImageUrl = avatarUrl;
        if (!avatarImageUrl) {
            try {
                // Use Roblox's avatar API for high-quality images
                const avatarResponse = await this.makeRobloxRequest(`thumbnails.roblox.com`, `/v1/users/avatar-bust?userIds=${userInfo.id}&size=420x420&format=Png&isCircular=false`);
                if (avatarResponse.data && avatarResponse.data[0] && avatarResponse.data[0].imageUrl) {
                    avatarImageUrl = avatarResponse.data[0].imageUrl;
                }
            } catch (error) {
                console.log('Failed to get avatar from API, using fallback');
                avatarImageUrl = `https://www.roblox.com/headshot-thumbnail/image?userId=${userInfo.id}&width=420&height=420&format=png`;
            }
        }

        // Create a beautiful embed exactly like Bloxlink's style
        const embed = new EmbedBuilder()
            .setColor('#FF6B35') // Roblox-inspired orange color
            .setThumbnail(avatarImageUrl)
            .setTimestamp()
            .setFooter({
                text: 'RankBreaker • Roblox Integration',
                iconURL: 'https://cdn.discordapp.com/icons/1362356687092191442/25b53ae035c74c9d6edcf8ca11dfc205.webp'
            });

        // Main title with username and link (exactly like Bloxlink's format)
        const displayName = userInfo.displayName && userInfo.displayName !== userInfo.name ?
            `${userInfo.displayName} (@${userInfo.name})` : userInfo.name;

        embed.setTitle(`${displayName}`);
        embed.setURL(`https://www.roblox.com/users/${userInfo.id}/profile`);

        // Roblox Information Section (like Bloxlink)
        let robloxInfo = '';
        robloxInfo += `### @${userInfo.name}\n`;
        if (userInfo.created) {
            robloxInfo += `Account Created: <t:${Math.floor(new Date(userInfo.created).getTime() / 1000)}:F>\n`;
        }

        embed.addFields({
            name: '## Roblox Information',
            value: robloxInfo,
            inline: true
        });

        // Discord Information Section (if available, like Bloxlink)
        if (discordUser) {
            let discordInfo = '';
            discordInfo += `### @${discordUser.username}\n`;
            discordInfo += `Account Created: <t:${Math.floor(discordUser.createdTimestamp / 1000)}:F>\n`;

            embed.addFields({
                name: '## Discord Information',
                value: discordInfo,
                inline: true
            });
        }

        // Description section (if available, like Bloxlink)
        if (userInfo.description && userInfo.description.trim()) {
            const description = userInfo.description.length > 1024 ?
                userInfo.description.substring(0, 1021) + '...' : userInfo.description;

            embed.addFields({
                name: '## Description',
                value: description,
                inline: false
            });
        }

        return embed;
    },

    // Helper function for Roblox API requests
    async makeRobloxRequest(hostname, path) {
        return new Promise((resolve, reject) => {
            const options = {
                hostname: hostname,
                path: path,
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            };

            const req = https.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const parsedData = JSON.parse(data);
                        resolve(parsedData);
                    } catch (error) {
                        reject(new Error(`Failed to parse response: ${error.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.end();
        });
    },

    makeDiscordRequest(endpoint, options = {}) {
        return new Promise((resolve, reject) => {
            const defaultOptions = {
                hostname: 'discord.com',
                path: `/api/v10/${endpoint}`,
                method: 'GET',
                headers: {
                    'Authorization': config.tokenDungeon,
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
            };

            const requestOptions = { ...defaultOptions, ...options };

            const req = https.request(requestOptions, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const parsedData = JSON.parse(data);
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(parsedData);
                        } else {
                            console.error(`Discord API Error (${res.statusCode}):`, parsedData);
                            console.error(`Request details: ${requestOptions.method} ${requestOptions.hostname}${requestOptions.path}`);
                            console.error(`Headers:`, requestOptions.headers);
                            reject(new Error(`Discord API returned status code ${res.statusCode}: ${JSON.stringify(parsedData)}`));
                        }
                    } catch (error) {
                        reject(new Error(`Failed to parse Discord API response: ${error.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`Discord API request failed: ${error.message}`));
            });

            if (requestOptions.body) {
                // Handle usePayloadJSON option like discord.js-selfbot-v13
                if (requestOptions.usePayloadJSON) {
                    // For interactions, use multipart/form-data format
                    const boundary = '----formdata-discord-' + Math.random().toString(16).substring(2);
                    requestOptions.headers['Content-Type'] = `multipart/form-data; boundary=${boundary}`;

                    const payload = JSON.stringify(requestOptions.body);
                    console.log(`📤 Multipart payload preview:`, payload.substring(0, 200) + '...');

                    const formData = Buffer.concat([
                        Buffer.from(`--${boundary}\r\n`),
                        Buffer.from('Content-Disposition: form-data; name="payload_json"\r\n'),
                        Buffer.from('Content-Type: application/json\r\n\r\n'),
                        Buffer.from(payload),
                        Buffer.from(`\r\n--${boundary}--\r\n`)
                    ]);

                    console.log(`📤 Form data size: ${formData.length} bytes`);
                    req.write(formData);
                } else {
                    const jsonPayload = JSON.stringify(requestOptions.body);
                    console.log(`📤 JSON payload preview:`, jsonPayload.substring(0, 200) + '...');
                    req.write(jsonPayload);
                }
            }

            req.end();
        });
    },

    generateNonce() {
        return (BigInt(Date.now()) << BigInt(22)).toString();
    },

    generateSessionId() {
        return Array.from({length: 32}, () => Math.floor(Math.random() * 16).toString(16)).join('');
    }
};
