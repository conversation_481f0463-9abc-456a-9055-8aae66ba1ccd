const {
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uilder,
    <PERSON><PERSON><PERSON><PERSON>er,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    ChannelType,
    PermissionFlagsBits,
    AttachmentBuilder
} = require('discord.js');
const configService = require('../../../services/configService');
const chalk = require('chalk');
const { createCanvas } = require('canvas');

// Setup Emojis - Centralized emoji definitions for the setup system
const SETUP_EMOJIS = {
    // Status emojis
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    ENABLED: '<:enabled_RB:1384533989444030655>',
    DISABLED: '<:disabled_RB:1384534072675664002>',
    VALID: '✅',
    INVALID: '❌',

    // Feature emojis
    DUNGEONS: '⁠🌀',
    WORLD_BOSS: '👹',
    CHANNELS: '📢',
    ROLES: '🎭',
    GAME: '🎮',
    COMING_SOON: '🚧',
    INFERNAL_BOSS: '🏰',

    // Role type emojis
    RANK_ROLES: '🏆',
    WORLD_ROLES: '🌍',
    ISLAND_ROLES: '🏝️',
    SPECIAL_ROLES: '⭐',

    // Action emojis
    CREATE_NEW: '🆕',
    CREATE_ALL: '🎯',
    USE_EXISTING: '🎭',
    ENABLE: '1384575597656674446',
    DISABLE: '1384575688039727244',
    BACK: '🔙',
    CANCEL: '❌',

    // Process emojis
    LOADING: '⏳',
    PROGRESS: '🔄',
    PROGRESS_BAR: '📊',

    // Management emojis
    MANAGE: '🔧',
    DELETE: '🗑️',
    CLEANUP: '🧹',
    SETUP: '🧱',
    TARGET: '🎯',
    LOCK: '🔒',
    LABEL: '🏷️',

    // Special action emojis
    FINISH: '✅',
    CONFIRM: '✅',
    KEEP: '🔒',
    INFO: 'ℹ️',
    DISABLED: '🔴',
    ALL_SUCCESS: '✅',
    DISABLE_WARNING: '⚠️',
    KEEP_ROLES: '🔒',
    DELETE_ROLES: '🗑️',

    // Additional emojis for comprehensive coverage
    OVERWRITE: '⚠️',
    TIMEOUT: '⏰',
    MAPPED: '✅',
    NOT_SET: '❌',
    CONFIGURED: '✅',
    NOT_CONFIGURED: '❌',
    REMOVE: '🗑️',
    CLEAN: '🧹',
    ALL_SUCCESS: '✅',
    FAILED: '❌',
    DELETED: '🗑️',
    KEPT: '🔧',
    SUMMARY: '📊',

    // Additional missing emojis found in the code
    ASSIGN: '🎭',
    VALIDATE: '✅',
    INVALID_ROLE: '❌',
    ROLE_CREATED: '✅',
    ROLE_ASSIGNED: '✅',
    ROLE_REMOVED: '🗑️',
    MORE_SETUP: '🧱',

    // Additional emojis found throughout the file
    MAP: '🎯',
    CONFIRM_MAPPING: '✅',
    ROLE_MANAGEMENT: '🔧',
    VALID_ROLES: '✅',
    INVALID_ROLES: '❌',
    REMOVE_ROLES: '🗑️',
    CLEAN_INVALID: '🧹',
    REMOVE_ALL: '🗑️',
    CONFIRM_REMOVAL: '⚠️',
    REMOVE_CONFIG_ONLY: '🔧',
    REMOVE_AND_DELETE: '🗑️',
    CANCEL: '🔙',
    PROCESSING: '⏳',
    COMPLETE: '✅',
    CLEANUP_COMPLETE: '✅',
    MANAGE_ROLES: '🔧',
    CHANNEL_SELECTED: '✅',
    SETUP_PING_ROLES: '🧱',
    BACK_TO_MAIN: '🔙',
    FINISH_SETUP: '✅',
    ROLE_INPUT: '🎯',
    TIMEOUT_WARNING: '⏰',
    INVALID_FORMAT: '❌',
    ROLE_NOT_FOUND: '❌',
    CHANNEL_NOT_FOUND: '❌',
    INVALID_CHANNEL_TYPE: '❌',
    DISABLE_WARNING: '⚠️',
    KEEP_ROLES: '🔒',
    DELETE_ROLES: '🗑️',
    DELETED_COUNT: '🗑️',
    FAILED_COUNT: '❌',
    ALL_SUCCESS_DELETE: '✅',
    DISABLED_CONFIG: '🔒',
    TEST_SEND: '🧪'
};

// Session management for setup process
const setupSessions = new Map();
const SESSION_TIMEOUT = 10 * 60 * 1000; // 10 minutes in milliseconds

// Test dungeon cooldown management
const testDungeonCooldowns = new Map();
const TEST_DUNGEON_COOLDOWN = 60 * 1000; // 1 minute in milliseconds

// Test world boss cooldown management
const testWorldBossCooldowns = new Map();
const TEST_WORLDBOSS_COOLDOWN = 60 * 1000; // 1 minute in milliseconds


module.exports = {
    data: new SlashCommandBuilder()
        .setName('setup')
        .setDescription('Interactive setup for Arise Crossover features')
        .addSubcommand(subcommand =>
            subcommand
                .setName('arise_crossover')
                .setDescription('Set up Arise Crossover game features'),)
        //     .addStringOption(option =>
        //         option.setName('feature')
        //             .setDescription('Directly access a specific feature setup')
        // .addChoices(
        //     { name: 'Auto Dungeons', value: 'auto_dungeons' }
        //    // { name: 'Auto World Boss', value: 'auto_worldboss' }
        // )
        //             .setRequired(false)))
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild),
    name: 'setup',
    category: 'Info',
    aliases: [],
    cooldown: 10,
    usage: '/setup',
    description: 'Interactive setup for Arise Crossover features',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    BetaServer: true,
    CommandSupport: 'guild',
    async execute(interaction) {
        try {
            // Check permissions
            if (!interaction.member.permissions.has(PermissionFlagsBits.ManageGuild)) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} You need the "Manage Server" permission to use this command.`,
                    ephemeral: true
                });
            }

            const subcommand = interaction.options.getSubcommand();

            if (subcommand === 'arise_crossover') {
                const directFeature = interaction.options.getString('feature');

                if (directFeature) {
                    // Direct feature access
                    return await this.handleDirectFeatureAccess(interaction, directFeature);
                } else {
                    // Show main feature selection
                    return await this.showFeatureSelection(interaction);
                }
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error in setup command:`), error);

            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle(`${SETUP_EMOJIS.ERROR} Setup Error`)
                .setDescription('An error occurred during setup. Please try again or contact support.')
                .setTimestamp();

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ embeds: [errorEmbed], components: [] });
            } else {
                await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    async showFeatureSelection(interaction) {
        // Check for existing session
        const existingSession = setupSessions.get(interaction.user.id);
        if (existingSession) {
            // Calculate remaining time
            const currentTime = Date.now();
            const sessionStartTime = existingSession.startTime;
            const elapsedTime = currentTime - sessionStartTime;
            const remainingTime = SESSION_TIMEOUT - elapsedTime;

            if (remainingTime > 0) {
                const remainingMinutes = Math.ceil(remainingTime / (60 * 1000));
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.WARNING} You already have an active setup session. Please cancel it first.\n\nOr wait **${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}** and try again.`,
                    ephemeral: true
                });
            } else {
                // Session has expired, remove it
                setupSessions.delete(interaction.user.id);
            }
        }

        // Check current configuration
        const currentConfig = await configService.getServerConfig(interaction.guild.id);

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.GAME} Arise Crossover Setup`)
            .setDescription('Welcome to the interactive setup for Arise Crossover features!\n\nSelect a feature to configure:')
            .addFields([
                {
                    name: `${SETUP_EMOJIS.DUNGEONS} Auto Dungeons`,
                    value: currentConfig?.dungeonAlert?.enabled
                        ? `${SETUP_EMOJIS.ENABLED} Currently **Enabled**`
                        : `${SETUP_EMOJIS.DISABLED} Currently **Disabled**`,
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.WORLD_BOSS} Auto World Boss`,
                    value: currentConfig?.dungeonAlert?.enabled
                        ? `${SETUP_EMOJIS.ENABLED} Currently **Enabled**`
                        : `${SETUP_EMOJIS.DISABLED} Currently **Disabled**`,
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.INFERNAL_BOSS} Auto Infernal Castle Bosses`,
                    value: currentConfig?.infernalCastleAlert?.enabled
                        ? `${SETUP_EMOJIS.ENABLED} Currently **Enabled**`
                        : `${SETUP_EMOJIS.COMING_SOON} **Coming Soon**`,
                    inline: true
                }
            ])
            .setFooter({ text: 'Session will timeout in 10 minutes' })
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_auto_dungeons')
                    .setLabel(`${SETUP_EMOJIS.DUNGEONS} Auto Dungeons`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_auto_worldboss')
                    .setLabel(`${SETUP_EMOJIS.WORLD_BOSS} Auto World Boss`)
                    .setStyle(ButtonStyle.Primary),
                //.setDisabled(true),
                new ButtonBuilder()
                    .setCustomId('setup_auto_infernal_bosses')
                    .setLabel(`${SETUP_EMOJIS.INFERNAL_BOSS} Auto Infernal Castle Bosses`)
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(true)
                // new ButtonBuilder()
                //     .setCustomId('setup_cancel')
                //     .setLabel(`${SETUP_EMOJIS.CANCEL} Cancel`)
                //     .setStyle(ButtonStyle.Danger)
            );

        // Use update if interaction is already replied/deferred, otherwise reply
        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed], components: [row] });
        } else {
            await interaction.reply({ embeds: [embed], components: [row], ephemeral: true });
        }

        // Create session
        this.createSession(interaction.user.id, {
            step: 'feature_selection',
            guildId: interaction.guild.id,
            channelId: interaction.channel.id,
            startTime: Date.now()
        });
    },

    async handleDirectFeatureAccess(interaction, feature) {
        if (feature === 'auto_dungeons') {
            return await this.showAutoDungeonsSetup(interaction);
        } else if (feature === 'auto_worldboss') {
            return await this.showAutoWorldBossSetup(interaction);
        }
    },

    async showAutoWorldBossSetup(interaction) {
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        let worldBossConfig = currentConfig?.worldBossAlert;

        // Migrate old world boss role structure if needed
        if (worldBossConfig) {
            worldBossConfig = await this.migrateWorldBossConfig(interaction.guild.id, worldBossConfig);
        }

        // Check session for selected channel
        const session = this.getSession(interaction.user.id);
        const selectedChannelId = session?.selectedChannelId;

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.WORLD_BOSS} Auto World Boss Setup`)
            .setDescription('Configure automatic world boss alerts for your server.')
            .addFields([
                {
                    name: 'Current Status',
                    value: worldBossConfig?.enabled ? `${SETUP_EMOJIS.ENABLED} Enabled` : `${SETUP_EMOJIS.DISABLED} Disabled`,
                    inline: true
                },
                {
                    name: 'Target Channel',
                    value: selectedChannelId
                        ? `<#${selectedChannelId}> (Selected)`
                        : worldBossConfig?.targetChannelId
                            ? `<#${worldBossConfig.targetChannelId}>`
                            : 'Not configured',
                    inline: true
                }
            ])
            .setFooter({ text: 'Choose an option below to continue' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('worldboss_select_channel')
                    .setLabel(`${SETUP_EMOJIS.CHANNELS} Select Target Channel`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('worldboss_setup_roles')
                    .setLabel(`${SETUP_EMOJIS.ROLES} Setup Ping Roles`)
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('worldboss_test_send')
                    .setLabel(`${SETUP_EMOJIS.TEST_SEND} Test Send World Boss Alert`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('worldboss_toggle_enable')
                    .setLabel(worldBossConfig?.enabled ? `Disable Auto World Boss` : `Enable Auto World Boss`)
                    .setEmoji(worldBossConfig?.enabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(worldBossConfig?.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed], components: [row1, row2] });
        } else {
            await interaction.reply({ embeds: [embed], components: [row1, row2], ephemeral: true });
        }

        // Update or create session
        this.updateSession(interaction.user.id, {
            step: 'auto_worldboss_setup',
            guildId: interaction.guild.id,
            feature: 'auto_worldboss'
        });
    },

    async showAutoDungeonsSetup(interaction) {
        const currentConfig = await configService.getServerConfig(interaction.guild.id);
        const dungeonConfig = currentConfig?.dungeonAlert;

        // Check session for selected channel
        const session = this.getSession(interaction.user.id);
        const selectedChannelId = session?.selectedChannelId;

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.DUNGEONS} Auto Dungeons Setup`)
            .setDescription('Configure automatic dungeon alerts for your server.')
            .addFields([
                {
                    name: 'Current Status',
                    value: dungeonConfig?.enabled ? `${SETUP_EMOJIS.ENABLED} Enabled` : `${SETUP_EMOJIS.DISABLED} Disabled`,
                    inline: true
                },
                {
                    name: 'Target Channel',
                    value: selectedChannelId
                        ? `<#${selectedChannelId}> (Selected)`
                        : dungeonConfig?.targetChannelId
                            ? `<#${dungeonConfig.targetChannelId}>`
                            : 'Not configured',
                    inline: true
                }
            ])
            .setFooter({ text: 'Choose an option below to continue' })
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_select_channel')
                    .setLabel(`${SETUP_EMOJIS.CHANNELS} Select Target Channel`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel(`${SETUP_EMOJIS.ROLES} Setup Ping Roles`)
                    .setStyle(ButtonStyle.Secondary),
                new ButtonBuilder()
                    .setCustomId('dungeons_test_send')
                    .setLabel(`${SETUP_EMOJIS.TEST_SEND} Test Send Dungeon Embed`)
                    .setStyle(ButtonStyle.Secondary)
                // new ButtonBuilder()
                //     .setCustomId('setup_back')
                //     .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                //     .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_toggle_enable')
                    .setLabel(dungeonConfig?.enabled ? `Disable Auto Dungeons` : `Enable Auto Dungeons`)
                    .setEmoji(dungeonConfig?.enabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(dungeonConfig?.enabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        if (interaction.replied || interaction.deferred) {
            await interaction.editReply({ embeds: [embed], components: [row1, row2] });
        } else {
            await interaction.reply({ embeds: [embed], components: [row1, row2], ephemeral: true });
        }

        // Update or create session
        this.updateSession(interaction.user.id, {
            step: 'auto_dungeons_setup',
            guildId: interaction.guild.id,
            feature: 'auto_dungeons'
        });
    },

    createSession(userId, sessionData) {
        setupSessions.set(userId, sessionData);

        // Auto-cleanup session after timeout
        setTimeout(() => {
            setupSessions.delete(userId);
        }, SESSION_TIMEOUT);
    },

    updateSession(userId, updates) {
        const session = setupSessions.get(userId);
        if (session) {
            Object.assign(session, updates);
        } else {
            this.createSession(userId, updates);
        }
    },

    getSession(userId) {
        return setupSessions.get(userId);
    },

    clearSession(userId) {
        setupSessions.delete(userId);
    },

    // Helper method to get role color based on role type
    getRoleColor(rank) {
        switch (rank) {
            case 'E': return '#8897aa';
            case 'D': return '#4488ff';
            case 'C': return '#44aaff';
            case 'B': return '#6644ff';
            case 'A': return '#8844ff';
            case 'S': return '#aa44ff';
            case 'SS': return '#ff44ff';
            case 'G': return '#660066'; // Dark purple for G rank
            case 'N': return '#9900cc'; // Brighter purple for N rank
            case 'DUNGEON_PING': return '#00ff00';
            case 'RED_DUNGEON': return '#ff0000';
            case 'DOUBLE_DUNGEON': return '#ffaa00';
            default: return '#ffffff';
        }
    },

    // Handle button interactions
    async handleButtonInteraction(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} No active setup session found. Please start a new setup.`,
                    ephemeral: true
                });
            }


            const customId = interaction.customId;

            switch (customId) {
                case 'setup_auto_dungeons':
                    await this.showAutoDungeonsSetup(interaction);
                    break;

                case 'setup_auto_worldboss':
                    await this.showAutoWorldBossSetup(interaction);
                    break;

                case 'setup_cancel':
                    await this.handleCancel(interaction);
                    break;

                case 'setup_back':
                    await this.handleBackNavigation(interaction);
                    break;

                case 'dungeons_select_channel':
                    await this.showChannelSelection(interaction);
                    break;

                case 'dungeons_setup_roles':
                    await this.showRoleSelectionOptions(interaction);
                    break;

                case 'dungeons_toggle_enable':
                    await this.toggleAutoDungeons(interaction);
                    break;

                case 'dungeons_test_send':
                    await this.testSendDungeonEmbed(interaction);
                    break;

                case 'worldboss_select_channel':
                    await this.showChannelSelection(interaction, 'worldboss');
                    break;

                case 'worldboss_setup_roles':
                    await this.showWorldBossRoleSelection(interaction);
                    break;

                case 'worldboss_toggle_enable':
                    await this.toggleAutoWorldBoss(interaction);
                    break;

                case 'worldboss_test_send':
                    await this.testSendWorldBossAlert(interaction);
                    break;

                case 'worldboss_use_existing_channel':
                    await this.showExistingChannels(interaction, 'worldboss');
                    break;

                case 'worldboss_create_new_channel':
                    await this.createNewChannel(interaction, 'worldboss');
                    break;

                case 'worldboss_general_ping':
                    await this.showWorldBossGeneralPingOptions(interaction);
                    break;

                case 'worldboss_bosses_pings':
                    await this.showWorldBossBossesPingOptions(interaction);
                    break;

                case 'worldboss_create_general_role':
                    await this.createWorldBossGeneralRole(interaction);
                    break;

                case 'worldboss_assign_existing_general':
                    await this.showExistingGeneralRoles(interaction);
                    break;

                case 'worldboss_create_boss_roles':
                    await this.createWorldBossBossRoles(interaction);
                    break;

                case 'worldboss_create_all_boss_roles':
                    await this.createAllWorldBossRoles(interaction);
                    break;

                case 'worldboss_create_all_roles':
                    await this.createAllWorldBossRoles(interaction);
                    break;

                case 'worldboss_assign_existing_boss_roles':
                    await this.showExistingBossRoles(interaction);
                    break;

                case 'worldboss_toggle_general_ping':
                    await this.toggleWorldBossGeneralPing(interaction);
                    break;

                case 'worldboss_toggle_bosses_ping':
                    await this.toggleWorldBossBossesPing(interaction);
                    break;

                case 'worldboss_create_general_role_confirm_overwrite':
                    await this.createWorldBossGeneralRole(interaction);
                    break;

                case 'worldboss_create_boss_roles_confirm_overwrite':
                    await this.createWorldBossBossRoles(interaction);
                    break;

                case 'worldboss_create_all_boss_roles_confirm_overwrite':
                    await this.createAllWorldBossRoles(interaction);
                    break;

                case 'create_missing_worldboss_roles':
                    await this.createMissingWorldBossRoles(interaction);
                    break;

                case 'create_missing_worldboss_all_roles':
                    await this.createMissingWorldBossAllRoles(interaction);
                    break;

                case 'disable_worldboss_bosses_keep':
                    await this.disableWorldBossRoles(interaction, 'bosses', false);
                    break;

                case 'disable_worldboss_bosses_delete':
                    await this.disableWorldBossRoles(interaction, 'bosses', true);
                    break;

                case 'disable_worldboss_general_keep':
                    await this.disableWorldBossRoles(interaction, 'general', false);
                    break;

                case 'disable_worldboss_general_delete':
                    await this.disableWorldBossRoles(interaction, 'general', true);
                    break;

                case 'dungeons_use_existing_channel':
                    await this.showExistingChannels(interaction);
                    break;

                case 'dungeons_create_new_channel':
                    await this.createNewChannel(interaction);
                    break;

                case 'dungeons_use_existing_roles':
                    await this.showRoleSelectionOptions(interaction);
                    break;

                case 'dungeons_create_new_roles':
                    await this.createNewRoles(interaction);
                    break;

                case 'dungeons_create_all_roles':
                    await this.createAllRoles(interaction);
                    break;

                case 'dungeons_create_all_roles_confirm_overwrite':
                    await this.createNewRoles(interaction);
                    break;

                case 'setup_rank_roles':
                    await this.setupRankRoles(interaction);
                    break;

                case 'setup_world_roles':
                    await this.setupWorldRoles(interaction);
                    break;

                case 'setup_island_roles':
                    await this.setupIslandRoles(interaction);
                    break;

                case 'setup_special_roles':
                    await this.setupSpecialRoles(interaction);
                    break;

                // Role creation handlers
                case 'create_rank_roles':
                case 'create_world_roles':
                case 'create_island_roles':
                case 'create_special_roles':
                    await this.createSpecificRoles(interaction, customId);
                    break;

                // Missing role creation handlers
                case 'create_missing_rank_roles':
                case 'create_missing_world_roles':
                case 'create_missing_island_roles':
                case 'create_missing_special_roles':
                case 'create_missing_all_roles':
                    await this.createMissingRoles(interaction, customId);
                    break;

                // Existing role selection handlers
                case 'select_existing_rank_roles':
                    await this.showExistingRoleSelection(interaction, 'rank');
                    break;

                case 'select_existing_world_roles':
                    await this.showExistingRoleSelection(interaction, 'world');
                    break;

                case 'select_existing_island_roles':
                    await this.showExistingRoleSelection(interaction, 'island');
                    break;

                case 'select_existing_special_roles':
                    await this.showExistingRoleSelection(interaction, 'special');
                    break;

                // Role management handlers
                case 'manage_existing_roles':
                    await this.showRoleManagement(interaction);
                    break;

                case 'unset_roles':
                    await this.showRoleUnsetting(interaction);
                    break;

                case 'cleanup_invalid_roles':
                    await this.cleanupInvalidRoles(interaction);
                    break;

                case 'unset_all_roles':
                    await this.handleRoleUnsetting(interaction);
                    break;

                case 'confirm_remove_config_only':
                case 'confirm_remove_and_delete':
                    await this.handleRoleDeletion(interaction);
                    break;

                case 'confirm_role_mapping':
                    await this.confirmRoleMapping(interaction);
                    break;

                // Role category toggle handlers
                case 'toggle_rank_roles':
                    await this.toggleRoleCategory(interaction, 'rank');
                    break;

                case 'toggle_world_roles':
                    await this.toggleRoleCategory(interaction, 'world');
                    break;

                case 'toggle_island_roles':
                    await this.toggleRoleCategory(interaction, 'island');
                    break;

                case 'toggle_special_roles':
                    await this.toggleRoleCategory(interaction, 'special');
                    break;



                default:
                    if (customId === 'select_role_to_assign') {
                        await this.handleRoleAssignmentSelection(interaction);

                    } else if (customId === 'select_boss_role_to_assign') {
                        await this.handleBossRoleSelectionForInput(interaction);
                    } else if (customId === 'select_existing_channel') {
                        await this.handleChannelSelection(interaction);
                    } else if (customId.startsWith('disable_') && customId.endsWith('_keep')) {
                        await this.handleDisableRoleKeep(interaction);
                    } else if (customId.startsWith('disable_') && customId.endsWith('_delete')) {
                        await this.handleDisableRoleDelete(interaction);
                    } else if (customId.startsWith('select_role_')) {
                        await this.handleRoleSelection(interaction);
                    } else if (customId.startsWith('map_role_')) {
                        await this.handleRoleMapping(interaction);
                    } else if (customId.startsWith('unset_role_') || customId === 'select_roles_to_unset') {
                        await this.handleRoleUnsetting(interaction);
                    } else if (customId.startsWith('delete_role_')) {
                        await this.handleRoleDeletion(interaction);
                    }
                    break;
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling button interaction:`), error);

            const errorMessage = this.getDetailedErrorMessage(error);

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: errorMessage,
                    embeds: [],
                    components: []
                });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    ephemeral: true
                });
            }
        }
    },

    async handleCancel(interaction) {
        this.clearSession(interaction.user.id);

        const embed = new EmbedBuilder()
            .setColor('#ff9900')
            .setTitle(`${SETUP_EMOJIS.CANCEL} Setup Cancelled`)
            .setDescription('Setup has been cancelled. You can start a new setup anytime using `/setup arise_crossover`.')
            .setTimestamp();

        await interaction.update({ embeds: [embed], components: [] });
    },

    async handleBackNavigation(interaction) {
        const session = this.getSession(interaction.user.id);
        if (!session) {
            return await interaction.update({
                content: `${SETUP_EMOJIS.ERROR} No active setup session found. Please start a new setup.`,
                embeds: [],
                components: []
            });
        }

        // Navigate back based on current step
        switch (session.step) {
            case 'auto_dungeons_setup':
                await this.showFeatureSelection(interaction);
                break;
            case 'auto_worldboss_setup':
                await this.showFeatureSelection(interaction);
                break;
            case 'channel_selection':
            case 'existing_channel_selection':
            case 'channel_configured':
            case 'channel_created':
                if (session.feature === 'auto_worldboss') {
                    await this.showAutoWorldBossSetup(interaction);
                } else {
                    await this.showAutoDungeonsSetup(interaction);
                }
                break;
            case 'role_selection_options':
                await this.showAutoDungeonsSetup(interaction);
                break;
            case 'worldboss_role_selection':
                await this.showAutoWorldBossSetup(interaction);
                break;
            case 'worldboss_general_ping_options':
            case 'worldboss_bosses_ping_options':
                await this.showWorldBossRoleSelection(interaction);
                break;
            case 'worldboss_general_role_created':
            case 'worldboss_boss_roles_created':
                await this.showAutoWorldBossSetup(interaction);
                break;
            case 'rank_roles_setup':
            case 'world_roles_setup':
            case 'island_roles_setup':
            case 'special_roles_setup':
                await this.showRoleSelectionOptions(interaction);
                break;
            case 'rank_role_assignment':
            case 'world_role_assignment':
            case 'island_role_assignment':
            case 'special_role_assignment':
                // Go back to the specific role setup
                const roleType = session.currentRoleType;
                if (roleType === 'rank') {
                    await this.setupRankRoles(interaction);
                } else if (roleType === 'world') {
                    await this.setupWorldRoles(interaction);
                } else if (roleType === 'island') {
                    await this.setupIslandRoles(interaction);
                } else if (roleType === 'special') {
                    await this.setupSpecialRoles(interaction);
                } else {
                    await this.showRoleSelectionOptions(interaction);
                }
                break;
            case 'role_management':
            case 'role_unsetting':
            case 'role_removal_confirmation':
            case 'disable_role_confirmation':
                await this.showRoleSelectionOptions(interaction);
                break;
            case 'channel_selected':
                await this.showAutoDungeonsSetup(interaction);
                break;
            case 'role_mapping':
                // Go back to the specific role setup based on roleType
                const mappingRoleType = session.roleType;
                if (mappingRoleType === 'rank') {
                    await this.setupRankRoles(interaction);
                } else if (mappingRoleType === 'world') {
                    await this.setupWorldRoles(interaction);
                } else if (mappingRoleType === 'island') {
                    await this.setupIslandRoles(interaction);
                } else if (mappingRoleType === 'special') {
                    await this.setupSpecialRoles(interaction);
                } else {
                    await this.showRoleSelectionOptions(interaction);
                }
                break;
            case 'roles_created':
                await this.showRoleSelectionOptions(interaction);
                break;
            default:
                await this.showFeatureSelection(interaction);
                break;
        }
    },

    getDetailedErrorMessage(error) {
        if (error.code === 50013) {
            return `${SETUP_EMOJIS.ERROR} I don\'t have the required permissions. Please ensure I have "Manage Roles" and "Manage Channels" permissions.`;
        } else if (error.code === 50001) {
            return `${SETUP_EMOJIS.ERROR} Missing access to the specified channel or role. Please check permissions.`;
        } else if (error.code === 10011) {
            return `${SETUP_EMOJIS.ERROR} The specified role was not found. It may have been deleted.`;
        } else if (error.code === 10003) {
            return `${SETUP_EMOJIS.ERROR} The specified channel was not found. It may have been deleted.`;
        } else if (error.message?.includes('timeout')) {
            return `${SETUP_EMOJIS.ERROR} The operation timed out. Please try again.`;
        } else {
            return `${SETUP_EMOJIS.ERROR} An unexpected error occurred. Please try again or contact support if the issue persists.`;
        }
    },

    async showChannelSelection(interaction, feature = 'dungeons') {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const isWorldBoss = feature === 'worldboss';
        const currentChannelId = isWorldBoss
            ? config?.worldBossAlert?.targetChannelId
            : config?.dungeonAlert?.targetChannelId;

        // Check session for selected channel (not yet saved to database)
        const session = this.getSession(interaction.user.id);
        const selectedChannelId = session?.selectedChannelId;

        const alertType = isWorldBoss ? 'world boss alerts' : 'dungeon alerts';
        const channelName = isWorldBoss ? 'world-boss' : 'dungeons';

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.CHANNELS} Channel Selection`)
            .setDescription(`Choose how you want to set up the target channel for ${alertType}:`)
            .addFields([
                {
                    name: 'Current Configuration',
                    value: selectedChannelId
                        ? `<#${selectedChannelId}> (Selected - not saved yet)`
                        : currentChannelId
                            ? `<#${currentChannelId}> (Currently configured)`
                            : `${SETUP_EMOJIS.ERROR} Not configured`,
                    inline: false
                },
                {
                    name: 'Options',
                    value: `• **Use Existing**: Select from your server's existing text channels\n• **Create New**: Create a new #${channelName} channel automatically`,
                    inline: false
                }
            ])
            .setTimestamp();

        const customIdPrefix = isWorldBoss ? 'worldboss' : 'dungeons';
        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`${customIdPrefix}_use_existing_channel`)
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing Channel`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`${customIdPrefix}_create_new_channel`)
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New Channel`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, {
            step: 'channel_selection',
            feature: feature
        });
    },

    async toggleAutoDungeons(interaction) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);

            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    },
                    worldBossAlert: { enabled: false },
                    infernalAlert: { enabled: false }
                };
            }

            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                };
            }

            // Toggle the enabled state
            const newState = !config.dungeonAlert.enabled;
            config.dungeonAlert.enabled = newState;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Show updated setup
            await this.showAutoDungeonsSetup(interaction);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error toggling auto dungeons:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to toggle auto dungeons. Please try again.`,
                ephemeral: true
            });
        }
    },

    async testSendDungeonEmbed(interaction) {
        try {
            // Check cooldown first
            const userId = interaction.user.id;
            const now = Date.now();
            const lastUsed = testDungeonCooldowns.get(userId);

            if (lastUsed && (now - lastUsed) < TEST_DUNGEON_COOLDOWN) {
                const remainingTime = Math.ceil((TEST_DUNGEON_COOLDOWN - (now - lastUsed)) / 1000);
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.TIMEOUT} Please wait **${remainingTime} seconds** before testing dungeon alert again.`,
                    ephemeral: true
                });
            }

            // Get current configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            const dungeonConfig = config?.dungeonAlert;

            // Check session for selected channel (not yet saved to database)
            const session = this.getSession(interaction.user.id);
            const selectedChannelId = session?.selectedChannelId;

            // Determine target channel
            const targetChannelId = selectedChannelId || dungeonConfig?.targetChannelId;

            if (!targetChannelId) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} No channel is set up for dungeon alerts. Please set up a channel first before testing.`,
                    ephemeral: true
                });
            }

            // Validate channel exists
            const targetChannel = interaction.guild.channels.cache.get(targetChannelId);
            if (!targetChannel) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} The configured channel <#${targetChannelId}> was not found. Please set up a valid channel first.`,
                    ephemeral: true
                });
            }

            // Check if bot has permission to send messages in the target channel
            if (!targetChannel.permissionsFor(interaction.guild.members.me).has(PermissionFlagsBits.SendMessages)) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} I don't have permission to send messages in ${targetChannel}. Please check my permissions.`,
                    ephemeral: true
                });
            }

            await interaction.reply({
                content: `${SETUP_EMOJIS.LOADING} Sending test dungeon embed to ${targetChannel}...`,
                ephemeral: true
            });

            // Create test dungeon info (E rank dungeon)
            const testDungeonInfo = {
                rank: 'E',
                world: 1,
                island: 'Leveling City',
                boss: 'Vermillion',
                isRedDungeon: false,
                isDoubleDungeon: false
            };

            // Use the DungeonAlert class to create the image (same as real alerts)
            const DungeonAlert = require('../../../modules/dungeonAlert');
            const dungeonAlert = new DungeonAlert(interaction.client);
            const attachment = await dungeonAlert.createDungeonImage(testDungeonInfo);

            // Create test dungeon embed using the same format as real alerts
            const testEmbed = new EmbedBuilder()
                .setTitle('🎯 NEW DUNGEON ALERT — RANK E 🌐')
                .setColor(this.getRoleColor('E'))
                .setImage('attachment://dungeon-info.png')
                .setFooter({
                    text: `This is a test dungeon alert ran by ${interaction.user.displayName}`
                    //iconURL: interaction.user.displayAvatarURL()
                })
            //.setTimestamp();

            // Send the test embed to the target channel
            await targetChannel.send({
                //content: '🧪 **TEST DUNGEON ALERT** 🧪',
                embeds: [testEmbed],
                files: [attachment]
            });

            // Set cooldown for this user
            testDungeonCooldowns.set(userId, now);

            // Auto-cleanup cooldown after expiry
            setTimeout(() => {
                testDungeonCooldowns.delete(userId);
            }, TEST_DUNGEON_COOLDOWN);

            // Update the reply to show success
            await interaction.editReply({
                content: `${SETUP_EMOJIS.SUCCESS} Test dungeon embed sent successfully to ${targetChannel}! Check the channel to see how dungeon alerts will look.`
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error sending test dungeon embed:`), error);

            const errorMessage = this.getDetailedErrorMessage(error);

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: errorMessage
                });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    ephemeral: true
                });
            }
        }
    },

    async showExistingChannels(interaction, feature = 'dungeons') {
        // Get current configuration
        const config = await configService.getServerConfig(interaction.guild.id);
        const isWorldBoss = feature === 'worldboss';
        const currentChannelId = isWorldBoss
            ? config?.worldBossAlert?.targetChannelId
            : config?.dungeonAlert?.targetChannelId;
        const alertType = isWorldBoss ? 'world boss' : 'dungeon';

        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.LABEL} Set ${isWorldBoss ? 'World Boss' : 'Dungeon'} Channel`)
            .setDescription(`Provide the channel ID for ${alertType} alerts:`)
            .addFields([
                {
                    name: 'Current Configuration',
                    value: currentChannelId && interaction.guild.channels.cache.has(currentChannelId)
                        ? `<#${currentChannelId}> (Currently configured)`
                        : `${SETUP_EMOJIS.NOT_CONFIGURED} Not configured`,
                    inline: false
                },
                {
                    name: 'Instructions',
                    value: '1. **Send the Channel ID** in this chat\n2. You can get the Channel ID by right-clicking on a channel and selecting "Copy Channel ID"\n3. The bot will automatically delete your message and update the configuration\n\n*You have 60 seconds to respond.*',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({
            embeds: [embed],
            components: [row]
        });

        this.updateSession(interaction.user.id, {
            step: 'existing_channel_selection',
            pendingChannelInput: {
                channelId: interaction.channel.id,
                userId: interaction.user.id
            },
            feature: feature
        });

        // Create a collector to wait for the user's channel input
        const filter = (message) => message.author.id === interaction.user.id;
        const collector = interaction.channel.createMessageCollector({
            filter,
            time: 60000, // 1 minute timeout
            max: 1
        });

        collector.on('collect', async (message) => {
            try {
                await this.processChannelInput(message, interaction);
            } catch (error) {
                console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing channel input:`), error);
                await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
            }
        });

        collector.on('end', (collected) => {
            if (collected.size === 0) {
                interaction.followUp({
                    content: `${SETUP_EMOJIS.TIMEOUT_WARNING} Channel input timed out. Please try again.`,
                    ephemeral: true
                }).catch(console.error);
            }
        });
    },

    async createNewChannel(interaction, feature = 'dungeons') {
        try {
            // Check if bot has permission to create channels
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageChannels)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don\'t have permission to create channels. Please give me the "Manage Channels" permission or use an existing channel.`,
                    embeds: [],
                    components: []
                });
            }

            const isWorldBoss = feature === 'worldboss';
            const channelName = isWorldBoss ? 'world-boss' : 'dungeons';
            const alertType = isWorldBoss ? 'world boss' : 'dungeon';

            await interaction.update({
                content: `${SETUP_EMOJIS.LOADING} Creating channel... This may take a moment.`,
                embeds: [],
                components: []
            });

            // Create the channel
            const channel = await interaction.guild.channels.create({
                name: channelName,
                type: ChannelType.GuildText,
                topic: `Automatic ${alertType} alerts from Arise Crossover`,
                reason: `Created by setup command for ${alertType} alerts`
            });

            // Immediately save to MongoDB
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        targetChannelId: isWorldBoss ? undefined : channel.id,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    },
                    worldBossAlert: {
                        enabled: false,
                        targetChannelId: isWorldBoss ? channel.id : undefined,
                        worldBossRoles: {}
                    }
                };
            } else {
                if (isWorldBoss) {
                    if (!config.worldBossAlert) {
                        config.worldBossAlert = {
                            enabled: false,
                            targetChannelId: channel.id,
                            worldBossRoles: {}
                        };
                    } else {
                        config.worldBossAlert.targetChannelId = channel.id;
                    }
                } else {
                    if (!config.dungeonAlert) {
                        config.dungeonAlert = {
                            enabled: false,
                            targetChannelId: channel.id,
                            dungeonRoles: {},
                            worldRoles: {},
                            islandRoles: {}
                        };
                    } else {
                        config.dungeonAlert.targetChannelId = channel.id;
                    }
                }
            }
            await configService.saveServerConfig(interaction.guild.id, config);

            // Update session with selected channel
            this.updateSession(interaction.user.id, {
                selectedChannelId: channel.id,
                step: 'channel_selected'
            });

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} Channel Created & Configured`)
                .setDescription(`Successfully created ${channel} for ${alertType} alerts!`)
                .addFields([
                    {
                        name: 'Configuration Status',
                        value: `${SETUP_EMOJIS.SUCCESS} Channel has been automatically saved to the database`,
                        inline: false
                    },
                    {
                        name: 'Next Steps',
                        value: 'You can now set up ping roles or go back to the main setup.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const setupRolesCustomId = isWorldBoss ? 'worldboss_setup_roles' : 'dungeons_setup_roles';
            const backToSetupCustomId = isWorldBoss ? 'setup_auto_worldboss' : 'setup_auto_dungeons';
            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(backToSetupCustomId)
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to ${isWorldBoss ? 'World Boss' : 'Auto Dungeons'}`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId(setupRolesCustomId)
                        .setLabel(`${SETUP_EMOJIS.SETUP} Setup Ping Roles`)
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to Main Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            // Update session to ensure back navigation works correctly
            this.updateSession(interaction.user.id, {
                selectedChannelId: null, // Clear since it's now saved to database
                step: 'channel_created',
                feature: isWorldBoss ? 'auto_worldboss' : 'auto_dungeons'
            });

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating channel:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create channel. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },



    async showRoleSelectionOptions(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.DUNGEONS} Dungeons Ping Roles Setup`)
            .setDescription('Choose which types of roles you want to set up:')
            .addFields([
                {
                    name: `${SETUP_EMOJIS.RANK_ROLES} Rank Roles (E-N)`,
                    value: 'Roles for different dungeon ranks',
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.WORLD_ROLES} World Roles`,
                    value: 'World 1 and World 2 ping roles',
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.ISLAND_ROLES} Island Roles`,
                    value: 'Individual island ping roles',
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.SPECIAL_ROLES} Special Roles`,
                    value: 'Red Gate, Double Dungeon, General Ping',
                    inline: true
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_rank_roles')
                    .setLabel(`${SETUP_EMOJIS.RANK_ROLES} Rank Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_world_roles')
                    .setLabel(`${SETUP_EMOJIS.WORLD_ROLES} World Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_island_roles')
                    .setLabel(`${SETUP_EMOJIS.ISLAND_ROLES} Island Roles`)
                    .setStyle(ButtonStyle.Primary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_special_roles')
                    .setLabel(`${SETUP_EMOJIS.SPECIAL_ROLES} Special Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_all_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create All Roles`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, { step: 'role_selection_options' });
    },

    async showRoleSetup(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.SETUP} Role Setup`)
            .setDescription('Choose how you want to set up ping roles for dungeon alerts:')
            .addFields([
                {
                    name: `${SETUP_EMOJIS.USE_EXISTING} Use Existing Roles`,
                    value: 'Select from your server\'s existing roles',
                    inline: false
                },
                {
                    name: `${SETUP_EMOJIS.CREATE_NEW} Create New Roles`,
                    value: 'Automatically create all necessary roles with proper colors',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_use_existing_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('dungeons_create_new_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New Roles`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, { step: 'role_setup' });
    },

    async createNewRoles(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don\'t have permission to create roles. Please give me the "Manage Roles" permission or use existing roles.`,
                    embeds: [],
                    components: []
                });
            }

            // Check for existing roles in the config
            const config = await configService.getServerConfig(interaction.guild.id);
            const hasExistingRoles = config && config.dungeonAlert && (
                Object.keys(config.dungeonAlert.dungeonRoles || {}).length > 0 ||
                Object.keys(config.dungeonAlert.worldRoles || {}).length > 0 ||
                Object.keys(config.dungeonAlert.islandRoles || {}).length > 0
            );
            if (hasExistingRoles && !interaction.customId?.endsWith('_confirm_overwrite')) {
                // Show warning and require confirmation
                const warningEmbed = new EmbedBuilder()
                    .setColor('#ff9900')
                    .setTitle(`${SETUP_EMOJIS.OVERWRITE} Overwrite Existing Roles?`)
                    .setDescription('This will **delete all currently configured roles** in the database and create new ones.\n\nAre you sure you want to continue?')
                    .addFields([
                        { name: 'Current Configured Roles', value: 'This will overwrite all existing role configurations.', inline: false }
                    ])
                    .setFooter({ text: 'This action cannot be undone.' })
                    .setTimestamp();
                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('dungeons_create_all_roles_confirm_overwrite')
                            .setLabel(`${SETUP_EMOJIS.OVERWRITE} Overwrite & Create New Roles`)
                            .setStyle(ButtonStyle.Danger),
                        new ButtonBuilder()
                            .setCustomId('setup_back')
                            .setLabel(`${SETUP_EMOJIS.BACK} Cancel`)
                            .setStyle(ButtonStyle.Secondary)
                    );
                await interaction.update({ embeds: [warningEmbed], components: [row] });
                return;
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.LOADING} Creating roles... This may take a moment.`,
                embeds: [],
                components: []
            });

            // Get shared config for island names
            const configWrapper = require('../../../services/configWrapper');
            const sharedConfig = await configWrapper.getSharedConfig();
            const allIslands = [
                ...(sharedConfig?.worldIslands?.[1] || []),
                ...(sharedConfig?.worldIslands?.[2] || [])
            ];

            // Define role names
            const dungeonRoleNames = {
                E: 'E Dungeon Ping',
                D: 'D Dungeon Ping',
                C: 'C Dungeon Ping',
                B: 'B Dungeon Ping',
                A: 'A Dungeon Ping',
                S: 'S Dungeon Ping',
                SS: 'SS Dungeon Ping',
                G: 'G Dungeon Ping',
                N: 'N Dungeon Ping',
                DUNGEON_PING: 'Dungeons Ping',
                RED_DUNGEON: 'Red Gate Ping',
                DOUBLE_DUNGEON: 'Double Dungeon Ping'
            };

            const worldRoleNames = {
                1: 'World 1 Ping',
                2: 'World 2 Ping'
            };

            const islandRoleNames = {
                'Leveling City': 'Leveling City Ping',
                'Grass Village': 'Grass Village Ping',
                'Brum Island': 'Brum Island Ping',
                'Faceheal Town': 'Faceheal Town Ping',
                'Lucky Kingdom': 'Lucky Kingdom Ping',
                'Nipon City': 'Nipon City Ping',
                'Mori Town': 'Mori Town Ping',
                'Dragon City': 'Dragon City Ping',
                'XZ City': 'XZ City Ping',
                'Kindama City': 'Kindama City Ping',
                'Hunters City': 'Hunters City Ping',
                'Nen City': 'Nen City Ping',
                'Hurricane Town': 'Hurricane Town Ping',
                'Cursed High': 'Cursed High Ping'
            };

            const roleData = [
                // Rank roles
                { name: dungeonRoleNames.E, key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                { name: dungeonRoleNames.D, key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                { name: dungeonRoleNames.C, key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                { name: dungeonRoleNames.B, key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                { name: dungeonRoleNames.A, key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                { name: dungeonRoleNames.S, key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                { name: dungeonRoleNames.SS, key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') },
                { name: dungeonRoleNames.G, key: 'G', type: 'dungeon', color: this.getRoleColor('G') },
                { name: dungeonRoleNames.N, key: 'N', type: 'dungeon', color: this.getRoleColor('N') },
                // Special roles
                { name: dungeonRoleNames.DUNGEON_PING, key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                { name: dungeonRoleNames.RED_DUNGEON, key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                { name: dungeonRoleNames.DOUBLE_DUNGEON, key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') },
                // World roles
                { name: worldRoleNames[1], key: '1', type: 'world', color: '#00ff00' },
                { name: worldRoleNames[2], key: '2', type: 'world', color: '#ff0000' }
            ];

            // Add island roles
            allIslands.forEach(island => {
                roleData.push({
                    name: islandRoleNames[island] || `${island} Ping`,
                    key: island,
                    type: 'island',
                    color: '#3498db' // Blue color for island roles
                });
            });

            // Get session for channel info
            const session = this.getSession(interaction.user.id);

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.PROGRESS} Creating All Roles`)
                .setDescription('Please wait while I create all the necessary roles...')
                .addFields([
                    { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config?.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                    { name: '⚙️ Status', value: `🔄 Creating roles... (0/${roleData.length})`, inline: true }
                ])
                .setTimestamp();

            await interaction.update({
                embeds: [progressEmbed],
                components: []
            });

            const createdRoles = {};
            const dungeonRoles = {};
            const worldRoles = {};
            const islandRoles = {};
            const totalRoles = roleData.length;
            let roleCount = 0;

            for (const roleInfo of roleData) {
                try {
                    roleCount++;

                    // Update progress
                    progressEmbed.setFields(
                        { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config?.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                        { name: '⚙️ Status', value: `🔄 Creating roles... (${roleCount}/${totalRoles})\nCurrent: **${roleInfo.name}**`, inline: true }
                    );
                    await interaction.editReply({ embeds: [progressEmbed] });

                    const role = await interaction.guild.roles.create({
                        name: roleInfo.name,
                        color: roleInfo.color,
                        reason: 'Created by setup command for dungeon alerts'
                    });

                    createdRoles[roleInfo.key] = role.id;

                    if (roleInfo.type === 'world') {
                        worldRoles[roleInfo.key] = role.id;
                    } else if (roleInfo.type === 'island') {
                        islandRoles[roleInfo.key] = role.id;
                    } else {
                        dungeonRoles[roleInfo.key] = role.id;
                    }

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 150));
                } catch (error) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to create role ${roleInfo.name}:`), error);
                }
            }

            // Update progress to saving configuration
            progressEmbed.setFields(
                { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config?.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                { name: '⚙️ Status', value: '💾 Saving configuration...', inline: true }
            );
            await interaction.editReply({ embeds: [progressEmbed] });

            // Save configuration to MongoDB
            let newConfig = config;
            if (!newConfig) {
                newConfig = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: { enabled: false, dungeonRoles: {}, worldRoles: {}, islandRoles: {} }
                };
            }
            if (!newConfig.dungeonAlert) newConfig.dungeonAlert = { enabled: false, dungeonRoles: {}, worldRoles: {}, islandRoles: {} };
            newConfig.dungeonAlert.dungeonRoles = dungeonRoles;
            newConfig.dungeonAlert.worldRoles = worldRoles;
            newConfig.dungeonAlert.islandRoles = islandRoles;
            await configService.saveServerConfig(interaction.guild.id, newConfig);

            // Update session with created roles
            this.updateSession(interaction.user.id, {
                dungeonRoles,
                worldRoles,
                islandRoles,
                step: 'roles_created'
            });

            // Create success embed
            const successEmbed = new EmbedBuilder()
                .setTitle('✅ Auto Dungeons Setup Complete!')
                .setDescription('Dungeon alert system has been successfully configured for this server.\n\n**🚀 Configuration is now active!** The server will immediately start receiving dungeon alerts without requiring a bot restart.')
                .setColor('#2ecc71')
                .addFields(
                    { name: '📍 Alert Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config?.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                    { name: '🎭 Roles Created', value: `${Object.keys(createdRoles).length} roles`, inline: true },
                    { name: '🔔 Status', value: 'Active & Ready', inline: true },
                    {
                        name: 'Rank Roles',
                        value: Object.entries(dungeonRoles)
                            .filter(([key]) => ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'].includes(key))
                            .map(([, roleId]) => `<@&${roleId}>`)
                            .join(', ') || 'None',
                        inline: false
                    },
                    {
                        name: 'Special Roles',
                        value: Object.entries(dungeonRoles)
                            .filter(([key]) => ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(key))
                            .map(([, roleId]) => `<@&${roleId}>`)
                            .join(', ') || 'None',
                        inline: false
                    },
                    {
                        name: 'World & Island Roles',
                        value: [
                            ...Object.values(worldRoles).map(roleId => `<@&${roleId}>`),
                            ...Object.values(islandRoles).slice(0, 5).map(roleId => `<@&${roleId}>`)
                        ].join(', ') + (Object.keys(islandRoles).length > 5 ? ` +${Object.keys(islandRoles).length - 5} more` : ''),
                        inline: false
                    }
                )
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [successEmbed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating roles:`), error);

            // Try to update the interaction, fallback to reply if needed
            try {
                if (interaction.replied || interaction.deferred) {
                    await interaction.editReply({
                        content: `${SETUP_EMOJIS.ERROR} Failed to create roles. Please check my permissions and try again.`,
                        embeds: [],
                        components: []
                    });
                } else {
                    await interaction.update({
                        content: `${SETUP_EMOJIS.ERROR} Failed to create roles. Please check my permissions and try again.`,
                        embeds: [],
                        components: []
                    });
                }
            } catch (replyError) {
                console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error sending error message:`), replyError);
            }
        }
    },

    async createAllRoles(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don\'t have permission to create roles. Please give me the "Manage Roles" permission or use existing roles.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration from database
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    }
                };
            }
            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                };
            }

            // Get shared config for island names
            const configWrapper = require('../../../services/configWrapper');
            const sharedConfig = await configWrapper.getSharedConfig();
            const allIslands = [
                ...(sharedConfig?.worldIslands?.[1] || []),
                ...(sharedConfig?.worldIslands?.[2] || [])
            ];

            // Define all roles that would be created
            const dungeonRoleNames = {
                E: 'E Dungeon Ping',
                D: 'D Dungeon Ping',
                C: 'C Dungeon Ping',
                B: 'B Dungeon Ping',
                A: 'A Dungeon Ping',
                S: 'S Dungeon Ping',
                SS: 'SS Dungeon Ping',
                G: 'G Dungeon Ping',
                N: 'N Dungeon Ping',
                DUNGEON_PING: 'Dungeons Ping',
                RED_DUNGEON: 'Red Gate Ping',
                DOUBLE_DUNGEON: 'Double Dungeon Ping'
            };

            const worldRoleNames = {
                1: 'World 1 Ping',
                2: 'World 2 Ping'
            };

            const islandRoleNames = {
                'Leveling City': 'Leveling City Ping',
                'Grass Village': 'Grass Village Ping',
                'Brum Island': 'Brum Island Ping',
                'Faceheal Town': 'Faceheal Town Ping',
                'Lucky Kingdom': 'Lucky Kingdom Ping',
                'Nipon City': 'Nipon City Ping',
                'Mori Town': 'Mori Town Ping',
                'Dragon City': 'Dragon City Ping',
                'XZ City': 'XZ City Ping',
                'Kindama City': 'Kindama City Ping',
                'Hunters City': 'Hunters City Ping',
                'Nen City': 'Nen City Ping',
                'Hurricane Town': 'Hurricane Town Ping'
            };

            const allRolesToCreate = [
                // Rank roles
                { name: dungeonRoleNames.E, key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                { name: dungeonRoleNames.D, key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                { name: dungeonRoleNames.C, key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                { name: dungeonRoleNames.B, key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                { name: dungeonRoleNames.A, key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                { name: dungeonRoleNames.S, key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                { name: dungeonRoleNames.SS, key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') },
                { name: dungeonRoleNames.G, key: 'G', type: 'dungeon', color: this.getRoleColor('G') },
                { name: dungeonRoleNames.N, key: 'N', type: 'dungeon', color: this.getRoleColor('N') },
                // Special roles
                { name: dungeonRoleNames.DUNGEON_PING, key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                { name: dungeonRoleNames.RED_DUNGEON, key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                { name: dungeonRoleNames.DOUBLE_DUNGEON, key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') },
                // World roles
                { name: worldRoleNames[1], key: '1', type: 'world', color: '#00ff00' },
                { name: worldRoleNames[2], key: '2', type: 'world', color: '#ff0000' }
            ];

            // Add island roles
            allIslands.forEach(island => {
                allRolesToCreate.push({
                    name: islandRoleNames[island] || `${island} Ping`,
                    key: island,
                    type: 'island',
                    color: '#3498db' // Blue color for island roles
                });
            });

            // Convert MongoDB objects to plain objects to avoid MongoDB properties
            const plainDungeonRoles = JSON.parse(JSON.stringify(config.dungeonAlert.dungeonRoles || {}));
            const plainWorldRoles = JSON.parse(JSON.stringify(config.dungeonAlert.worldRoles || {}));
            const plainIslandRoles = JSON.parse(JSON.stringify(config.dungeonAlert.islandRoles || {}));

            const currentRoles = {
                dungeonRoles: plainDungeonRoles,
                worldRoles: plainWorldRoles,
                islandRoles: plainIslandRoles
            };

            // Check which roles already exist in the configuration
            const existingRoles = [];
            const missingRoles = [];

            console.log(chalk.blue(`🔍 Checking ${allRolesToCreate.length} roles for Create All Roles...`));

            for (const roleInfo of allRolesToCreate) {
                let roleId = null;
                if (roleInfo.type === 'world') {
                    roleId = currentRoles.worldRoles[roleInfo.key];
                } else if (roleInfo.type === 'island') {
                    roleId = currentRoles.islandRoles[roleInfo.key];
                } else {
                    roleId = currentRoles.dungeonRoles[roleInfo.key];
                }

                // Ensure roleId is a string and validate format
                const roleIdString = typeof roleId === 'string' ? roleId : String(roleId);
                const isValidId = roleId && /^\d{17,19}$/.test(roleIdString);

                // Check if role exists in Discord
                const roleExists = isValidId && interaction.guild.roles.cache.has(roleIdString);

                if (roleExists) {
                    existingRoles.push({ ...roleInfo, roleId: roleIdString });
                    console.log(chalk.green(`✅ ${roleInfo.name} exists: ${roleIdString}`));
                } else {
                    missingRoles.push(roleInfo);
                    console.log(chalk.yellow(`❌ ${roleInfo.name} missing (roleId: ${roleId || 'none'}, valid: ${isValidId}, exists: ${roleExists})`));
                }
            }

            console.log(chalk.blue(`📊 Results: ${existingRoles.length} existing, ${missingRoles.length} missing`));

            // If all roles exist, show "All Roles Already Configured" message
            if (existingRoles.length === allRolesToCreate.length) {
                const embed = new EmbedBuilder()
                    .setColor('#00ff00')
                    .setTitle(`${SETUP_EMOJIS.SUCCESS} All Roles Already Configured`)
                    .setDescription(`All dungeon alert roles are already configured and exist in this server.`)
                    .addFields([
                        {
                            name: `${SETUP_EMOJIS.SUCCESS} Configured Roles (${existingRoles.length})`,
                            value: `All ${existingRoles.length} roles are properly configured and exist in Discord.`,
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('dungeons_setup_roles')
                            .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('setup_back')
                            .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            // If some roles exist but some are missing, show confirmation dialog
            if (existingRoles.length > 0) {
                const embed = new EmbedBuilder()
                    .setColor('#ff9900')
                    .setTitle(`${SETUP_EMOJIS.WARNING} Some Roles Already Configured`)
                    .setDescription(`Some roles are already configured, but some are missing. Would you like me to create the missing ones?`)
                    .addFields([
                        {
                            name: `${SETUP_EMOJIS.SUCCESS} Already Configured (${existingRoles.length})`,
                            value: `${existingRoles.length} roles are already configured and exist in Discord.`,
                            inline: false
                        },
                        {
                            name: `${SETUP_EMOJIS.ERROR} Missing Roles (${missingRoles.length})`,
                            value: missingRoles.slice(0, 10).map(role => `**${role.name}**`).join(', ') +
                                (missingRoles.length > 10 ? ` +${missingRoles.length - 10} more` : ''),
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('create_missing_all_roles')
                            .setLabel(`${SETUP_EMOJIS.SUCCESS} Yes, Create Missing Roles`)
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('dungeons_setup_roles')
                            .setLabel(`${SETUP_EMOJIS.CANCEL} No, Go Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                // Store missing roles in session for later use
                this.updateSession(interaction.user.id, {
                    missingRoles: missingRoles,
                    roleCategory: 'All Roles'
                });

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            // If no roles exist, proceed with normal creation
            await this.proceedWithRoleCreation(interaction, allRolesToCreate, 'All Roles', currentRoles, config);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error in createAllRoles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },


    async setupRankRoles(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentRoles = config?.dungeonAlert?.dungeonRoles || {};
        const isEnabled = config?.dungeonAlert?.rankRolesEnabled !== false; // Default to true

        const rankKeys = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'];
        const roleStatus = rankKeys.map(key => {
            const roleId = currentRoles[key];
            const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
            return `**${key} Rank**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.ERROR} Not Set`}`;
        }).join('\n');

        const configuredCount = rankKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.RANK_ROLES} Rank Roles Setup`)
            .setDescription('Choose how to set up rank roles (E, D, C, B, A, S, SS, G, N):')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${rankKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: roleStatus,
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all rank roles with proper colors\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_rank_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_rank_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_rank_roles')
                    .setLabel(isEnabled ? `Disable Rank Roles` : `Enable Rank Roles`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, {
            step: 'rank_roles_setup',
            currentRoleType: 'rank'
        });
    },

    async setupWorldRoles(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentRoles = config?.dungeonAlert?.worldRoles || {};
        const isEnabled = config?.dungeonAlert?.worldRolesEnabled !== false; // Default to true

        const worldKeys = ['1', '2'];
        const roleStatus = worldKeys.map(key => {
            const roleId = currentRoles[key];
            const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
            return `**World ${key} Ping**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.NOT_SET} Not Set`}`;
        }).join('\n');

        const configuredCount = worldKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.WORLD_ROLES} World Roles Setup`)
            .setDescription('Choose how to set up world ping roles (World 1, World 2):')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${worldKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: roleStatus,
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create world ping roles\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_world_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_world_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_world_roles')
                    .setLabel(isEnabled ? `Disable World Roles` : `Enable World Roles`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, {
            step: 'world_roles_setup',
            currentRoleType: 'world'
        });
    },

    async setupIslandRoles(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentRoles = config?.dungeonAlert?.islandRoles || {};
        const isEnabled = config?.dungeonAlert?.islandRolesEnabled !== false; // Default to true

        const islandKeys = ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town',
            'Lucky Kingdom', 'Nipon City', 'Mori Town', 'Dragon City',
            'XZ City', 'Kindama City', 'Hunters City', 'Nen City', 'Hurricane Town', 'Cursed High'];

        const configuredCount = islandKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.ISLAND_ROLES} Island Roles Setup`)
            .setDescription('Choose how to set up individual island ping roles:')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${islandKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: islandKeys.slice(0, 10).map(key => {
                        const roleId = currentRoles[key];
                        const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
                        return `**${key}**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.NOT_SET} Not Set`}`;
                    }).join('\n') + (islandKeys.length > 10 ? `\n*...and ${islandKeys.length - 10} more islands*` : ''),
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create roles for all islands\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_island_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_island_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_island_roles')
                    .setLabel(isEnabled ? `Disable Island Roles` : `Enable Island Roles`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, {
            step: 'island_roles_setup',
            currentRoleType: 'island'
        });
    },

    async setupSpecialRoles(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        const currentRoles = config?.dungeonAlert?.dungeonRoles || {};
        const isEnabled = config?.dungeonAlert?.specialRolesEnabled !== false; // Default to true

        const specialKeys = ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
        const specialNames = {
            'DUNGEON_PING': 'General Dungeon Ping',
            'RED_DUNGEON': 'Red Gate Ping',
            'DOUBLE_DUNGEON': 'Double Dungeon Ping'
        };

        const roleStatus = specialKeys.map(key => {
            const roleId = currentRoles[key];
            const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
            return `**${specialNames[key]}**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.NOT_SET} Not Set`}`;
        }).join('\n');

        const configuredCount = specialKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.SPECIAL_ROLES} Special Roles Setup`)
            .setDescription('Choose how to set up special ping roles:')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${specialKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: roleStatus,
                    inline: false
                },
                {
                    name: 'Special Roles Include',
                    value: '• **Dungeon Ping**: General dungeon notifications\n• **Red Gate**: Red dungeon alerts\n• **Double Dungeon**: Double dungeon alerts',
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all special roles\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('create_special_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('select_existing_special_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('toggle_special_roles')
                    .setLabel(isEnabled ? `Disable Special Roles` : `Enable Special Roles`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, {
            step: 'special_roles_setup',
            currentRoleType: 'special'
        });
    },

    async createSpecificRoles(interaction, roleType) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don\'t have permission to create roles. Please give me the "Manage Roles" permission.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration from database
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    }
                };
            }
            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                };
            }

            const session = this.getSession(interaction.user.id);

            // Convert MongoDB objects to plain objects to avoid MongoDB properties
            const plainDungeonRoles = JSON.parse(JSON.stringify(config.dungeonAlert.dungeonRoles || {}));
            const plainWorldRoles = JSON.parse(JSON.stringify(config.dungeonAlert.worldRoles || {}));
            const plainIslandRoles = JSON.parse(JSON.stringify(config.dungeonAlert.islandRoles || {}));

            const currentRoles = {
                dungeonRoles: plainDungeonRoles,
                worldRoles: plainWorldRoles,
                islandRoles: plainIslandRoles
            };

            console.log(chalk.blue(`🔍 Debug currentRoles for ${roleType}:`));
            console.log(chalk.blue(`📋 worldRoles:`, JSON.stringify(currentRoles.worldRoles, null, 2)));
            console.log(chalk.blue(`📋 dungeonRoles:`, JSON.stringify(currentRoles.dungeonRoles, null, 2)));
            console.log(chalk.blue(`📋 islandRoles:`, JSON.stringify(currentRoles.islandRoles, null, 2)));

            let rolesToCreate = [];
            let roleCategory = '';

            switch (roleType) {
                case 'create_rank_roles':
                    roleCategory = 'Rank Roles';
                    rolesToCreate = [
                        { name: 'E Dungeon Ping', key: 'E', type: 'dungeon', color: this.getRoleColor('E') },
                        { name: 'D Dungeon Ping', key: 'D', type: 'dungeon', color: this.getRoleColor('D') },
                        { name: 'C Dungeon Ping', key: 'C', type: 'dungeon', color: this.getRoleColor('C') },
                        { name: 'B Dungeon Ping', key: 'B', type: 'dungeon', color: this.getRoleColor('B') },
                        { name: 'A Dungeon Ping', key: 'A', type: 'dungeon', color: this.getRoleColor('A') },
                        { name: 'S Dungeon Ping', key: 'S', type: 'dungeon', color: this.getRoleColor('S') },
                        { name: 'SS Dungeon Ping', key: 'SS', type: 'dungeon', color: this.getRoleColor('SS') },
                        { name: 'G Dungeon Ping', key: 'G', type: 'dungeon', color: this.getRoleColor('G') },
                        { name: 'N Dungeon Ping', key: 'N', type: 'dungeon', color: this.getRoleColor('N') }
                    ];
                    break;

                case 'create_world_roles':
                    roleCategory = 'World Roles';
                    rolesToCreate = [
                        { name: 'World 1 Ping', key: '1', type: 'world', color: '#00ff00' },
                        { name: 'World 2 Ping', key: '2', type: 'world', color: '#ff0000' }
                    ];
                    break;

                case 'create_special_roles':
                    roleCategory = 'Special Roles';
                    rolesToCreate = [
                        { name: 'Dungeons Ping', key: 'DUNGEON_PING', type: 'dungeon', color: this.getRoleColor('DUNGEON_PING') },
                        { name: 'Red Gate Ping', key: 'RED_DUNGEON', type: 'dungeon', color: this.getRoleColor('RED_DUNGEON') },
                        { name: 'Double Dungeon Ping', key: 'DOUBLE_DUNGEON', type: 'dungeon', color: this.getRoleColor('DOUBLE_DUNGEON') }
                    ];
                    break;

                case 'create_island_roles':
                    roleCategory = 'Island Roles';
                    const configWrapper = require('../../../services/configWrapper');
                    const sharedConfig = await configWrapper.getSharedConfig();
                    const allIslands = [
                        ...(sharedConfig?.worldIslands?.[1] || []),
                        ...(sharedConfig?.worldIslands?.[2] || [])
                    ];

                    const islandRoleNames = {
                        'Leveling City': 'Leveling City Ping',
                        'Grass Village': 'Grass Village Ping',
                        'Brum Island': 'Brum Island Ping',
                        'Faceheal Town': 'Faceheal Town Ping',
                        'Lucky Kingdom': 'Lucky Kingdom Ping',
                        'Nipon City': 'Nipon City Ping',
                        'Mori Town': 'Mori Town Ping',
                        'Dragon City': 'Dragon City Ping',
                        'XZ City': 'XZ City Ping',
                        'Kindama City': 'Kindama City Ping',
                        'Hunters City': 'Hunters City Ping',
                        'Nen City': 'Nen City Ping',
                        'Hurricane Town': 'Hurricane Town Ping',
                        'Cursed High': 'Cursed High Ping'
                    };

                    rolesToCreate = allIslands.map(island => ({
                        name: islandRoleNames[island] || `${island} Ping`,
                        key: island,
                        type: 'island',
                        color: '#3498db'
                    }));
                    break;
            }

            // Check which roles already exist in the configuration
            const existingRoles = [];
            const missingRoles = [];

            console.log(chalk.blue(`🔍 Checking ${rolesToCreate.length} roles for ${roleCategory}...`));

            for (const roleInfo of rolesToCreate) {
                let roleId = null;
                if (roleInfo.type === 'world') {
                    roleId = currentRoles.worldRoles[roleInfo.key];
                    console.log(chalk.blue(`🔍 World role lookup: key="${roleInfo.key}", found="${roleId}", type="${typeof roleId}"`));
                } else if (roleInfo.type === 'island') {
                    roleId = currentRoles.islandRoles[roleInfo.key];
                    console.log(chalk.blue(`🔍 Island role lookup: key="${roleInfo.key}", found="${roleId}", type="${typeof roleId}"`));
                } else {
                    roleId = currentRoles.dungeonRoles[roleInfo.key];
                    console.log(chalk.blue(`🔍 Dungeon role lookup: key="${roleInfo.key}", found="${roleId}", type="${typeof roleId}"`));
                }

                // Ensure roleId is a string and validate format
                const roleIdString = typeof roleId === 'string' ? roleId : String(roleId);
                const isValidId = roleId && /^\d{17,19}$/.test(roleIdString);

                // Check if role exists in Discord
                const roleExists = isValidId && interaction.guild.roles.cache.has(roleIdString);

                if (roleExists) {
                    existingRoles.push({ ...roleInfo, roleId: roleIdString });
                    console.log(chalk.green(`✅ ${roleInfo.name} exists: ${roleIdString}`));
                } else {
                    missingRoles.push(roleInfo);
                    console.log(chalk.yellow(`❌ ${roleInfo.name} missing (roleId: ${roleId || 'none'}, valid: ${isValidId}, exists: ${roleExists})`));
                }
            }

            console.log(chalk.blue(`📊 Results: ${existingRoles.length} existing, ${missingRoles.length} missing`));

            // If all roles exist, show "All Roles Already Configured" message
            if (existingRoles.length === rolesToCreate.length) {
                // Safely format role mentions, ensuring roleId is a valid string
                const roleList = existingRoles
                    .map(role => {
                        const roleId = typeof role.roleId === 'string' ? role.roleId : String(role.roleId);
                        // Validate it's a Discord ID format
                        if (/^\d{17,19}$/.test(roleId)) {
                            return `<@&${roleId}>`;
                        } else {
                            return `**${role.name}** (Invalid ID)`;
                        }
                    })
                    .join(', ');

                const embed = new EmbedBuilder()
                    .setColor('#00ff00')
                    .setTitle(`${SETUP_EMOJIS.SUCCESS} All ${roleCategory} Already Configured`)
                    .setDescription(`All ${roleCategory.toLowerCase()} are already configured and exist in this server.`)
                    .addFields([
                        {
                            name: 'Configured Roles',
                            value: roleList || 'None found',
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('dungeons_setup_roles')
                            .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('setup_back')
                            .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            // If some roles exist but some are missing, show confirmation dialog
            if (existingRoles.length > 0) {
                // Safely format existing role mentions
                const existingRoleList = existingRoles
                    .map(role => {
                        const roleId = typeof role.roleId === 'string' ? role.roleId : String(role.roleId);
                        // Validate it's a Discord ID format
                        if (/^\d{17,19}$/.test(roleId)) {
                            return `<@&${roleId}>`;
                        } else {
                            return `**${role.name}** (Invalid ID)`;
                        }
                    })
                    .join(', ');

                // Format missing role names
                const missingRoleList = missingRoles
                    .map(role => `**${role.name}**`)
                    .join(', ');

                const embed = new EmbedBuilder()
                    .setColor('#ff9900')
                    .setTitle(`${SETUP_EMOJIS.WARNING} Some ${roleCategory} Already Configured`)
                    .setDescription(`Some ${roleCategory.toLowerCase()} are already configured, but some are missing. Would you like me to create the missing ones?`)
                    .addFields([
                        {
                            name: `${SETUP_EMOJIS.SUCCESS} Already Configured (${existingRoles.length})`,
                            value: existingRoleList || 'None found',
                            inline: false
                        },
                        {
                            name: `${SETUP_EMOJIS.ERROR} Missing Roles (${missingRoles.length})`,
                            value: missingRoleList || 'None',
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId(roleType.replace('create_', 'create_missing_'))
                            .setLabel(`${SETUP_EMOJIS.SUCCESS} Yes, Create Missing Roles`)
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('dungeons_setup_roles')
                            .setLabel(`${SETUP_EMOJIS.CANCEL} No, Go Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                // Store missing roles in session for later use
                this.updateSession(interaction.user.id, {
                    missingRoles: missingRoles,
                    roleCategory: roleCategory
                });

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            // If no roles exist, proceed with normal creation
            await this.proceedWithRoleCreation(interaction, rolesToCreate, roleCategory, currentRoles, config);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating specific roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async proceedWithRoleCreation(interaction, rolesToCreate, roleCategory, currentRoles, config) {
        const session = this.getSession(interaction.user.id);

        // Create progress embed
        const progressEmbed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.PROGRESS} Creating ${roleCategory}`)
            .setDescription('Please wait while I create the necessary roles...')
            .addFields([
                { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                { name: '⚙️ Status', value: `🔄 Creating roles... (0/${rolesToCreate.length})`, inline: true }
            ])
            .setTimestamp();

        await interaction.update({
            embeds: [progressEmbed],
            components: []
        });

        const createdRoles = [];
        const totalRoles = rolesToCreate.length;
        let roleCount = 0;

        for (const roleInfo of rolesToCreate) {
            try {
                roleCount++;

                // Update progress
                progressEmbed.setFields(
                    { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                    { name: '⚙️ Status', value: `🔄 Creating roles... (${roleCount}/${totalRoles})\nCurrent: **${roleInfo.name}**`, inline: true }
                );
                await interaction.editReply({ embeds: [progressEmbed] });

                const role = await interaction.guild.roles.create({
                    name: roleInfo.name,
                    color: roleInfo.color,
                    reason: `Created by setup command for ${roleCategory.toLowerCase()}`
                });

                createdRoles.push({ name: roleInfo.name, id: role.id });

                if (roleInfo.type === 'world') {
                    currentRoles.worldRoles[roleInfo.key] = role.id;
                } else if (roleInfo.type === 'island') {
                    currentRoles.islandRoles[roleInfo.key] = role.id;
                } else {
                    currentRoles.dungeonRoles[roleInfo.key] = role.id;
                }

                // Small delay to avoid rate limits
                await new Promise(resolve => setTimeout(resolve, 150));
            } catch (error) {
                console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to create role ${roleInfo.name}:`), error);
            }
        }

        // Update progress to saving configuration
        progressEmbed.setFields(
            { name: '📍 Target Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
            { name: '⚙️ Status', value: '💾 Saving configuration...', inline: true }
        );
        await interaction.editReply({ embeds: [progressEmbed] });

        // Save configuration to MongoDB
        config.dungeonAlert.dungeonRoles = currentRoles.dungeonRoles;
        config.dungeonAlert.worldRoles = currentRoles.worldRoles;
        config.dungeonAlert.islandRoles = currentRoles.islandRoles;
        await configService.saveServerConfig(interaction.guild.id, config);

        // Update session with created roles
        this.updateSession(interaction.user.id, currentRoles);

        // Create success embed
        const successEmbed = new EmbedBuilder()
            .setTitle(`✅ ${roleCategory} Setup Complete!`)
            .setDescription(`${roleCategory} have been successfully created and configured for this server.\n\n**🚀 Configuration is now active!** The server will immediately start using these roles for dungeon alerts.`)
            .setColor('#2ecc71')
            .addFields(
                { name: '📍 Alert Channel', value: session?.selectedChannelId ? `<#${session.selectedChannelId}>` : config.dungeonAlert?.targetChannelId ? `<#${config.dungeonAlert.targetChannelId}>` : 'Not configured', inline: true },
                { name: '🎭 Roles Created', value: `${createdRoles.length} roles`, inline: true },
                { name: '🔔 Status', value: 'Active & Ready', inline: true },
                {
                    name: 'Created Roles',
                    value: createdRoles.map(role => `<@&${role.id}>`).join(', ') || 'None',
                    inline: false
                }
            )
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('dungeons_setup_roles')
                    .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.editReply({ embeds: [successEmbed], components: [row] });
    },

    async createMissingRoles(interaction, customId) {
        try {
            console.log(chalk.blue(`🔧 Creating missing roles with customId: ${customId}`));

            const session = this.getSession(interaction.user.id);
            const missingRoles = session?.missingRoles;
            const roleCategory = session?.roleCategory;

            console.log(chalk.blue(`📋 Session data: ${missingRoles?.length || 0} missing roles, category: ${roleCategory}`));

            if (!missingRoles || missingRoles.length === 0) {
                console.log(chalk.red(`❌ No missing roles found in session`));
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No missing roles found in session. Please try again.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration from database
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    }
                };
            }
            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                };
            }

            const currentRoles = {
                dungeonRoles: { ...config.dungeonAlert.dungeonRoles },
                worldRoles: { ...config.dungeonAlert.worldRoles },
                islandRoles: { ...config.dungeonAlert.islandRoles }
            };

            // Proceed with creating only the missing roles
            await this.proceedWithRoleCreation(interaction, missingRoles, roleCategory, currentRoles, config);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating missing roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create missing roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },

    // Role validation and management methods
    async validateRoleExists(guild, roleId) {
        try {
            if (!guild || !roleId) return false;

            // Ensure roleId is a string
            const id = typeof roleId === 'string' ? roleId : String(roleId);

            // Check cache first
            const role = guild.roles.cache.get(id);
            if (role) return true;

            // Try to fetch from API if not in cache
            try {
                const fetchedRole = await guild.roles.fetch(id);
                return !!fetchedRole;
            } catch (fetchError) {
                return false;
            }
        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error validating role ${roleId}:`), error);
            return false;
        }
    },

    async validateRolesInDatabase(guild) {
        try {
            const config = await configService.getServerConfig(guild.id);
            if (!config?.dungeonAlert) return { valid: [], invalid: [] };

            const allRoles = {
                ...config.dungeonAlert.dungeonRoles,
                ...config.dungeonAlert.worldRoles,
                ...config.dungeonAlert.islandRoles
            };

            const valid = [];
            const invalid = [];

            for (const [key, roleId] of Object.entries(allRoles)) {
                if (roleId && await this.validateRoleExists(guild, roleId)) {
                    valid.push({ key, roleId });
                } else if (roleId) {
                    invalid.push({ key, roleId });
                }
            }

            return { valid, invalid };
        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error validating roles in database:`), error);
            return { valid: [], invalid: [] };
        }
    },

    async showExistingRoleSelection(interaction, roleType) {
        try {
            // Get current configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            const roleKeys = this.getRoleKeysForType(roleType);
            const currentRoles = this.getExistingRolesByType(config, roleType);

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.ASSIGN} Assign Existing ${this.getRoleTypeDisplayName(roleType)}`)
                .setDescription(`Select which ${roleType} role you want to assign:`)
                .addFields([
                    {
                        name: 'Instructions',
                        value: '1. Choose a role from the dropdown below\n2. After selecting, provide the role ID or mention the role in chat\n3. The role will be validated and assigned',
                        inline: false
                    }
                ])
                .setTimestamp();

            // Show current status if any roles are configured
            if (currentRoles.length > 0) {
                const statusText = currentRoles.map(role => {
                    const isValid = interaction.guild.roles.cache.has(role.roleId);
                    return `**${this.getRoleKeyDisplayName(role.key, roleType)}**: ${isValid ? `${SETUP_EMOJIS.VALIDATE} <@&${role.roleId}>` : `${SETUP_EMOJIS.INVALID_ROLE} Invalid Role`}`;
                }).join('\n');

                embed.addFields([
                    {
                        name: 'Current Configuration',
                        value: statusText,
                        inline: false
                    }
                ]);
            }

            // Create dropdown with role options
            const options = roleKeys.map(key => {
                const currentRole = currentRoles.find(r => r.key === key);
                const isConfigured = currentRole && interaction.guild.roles.cache.has(currentRole.roleId);

                return {
                    label: this.getRoleKeyDisplayName(key, roleType),
                    value: `assign_${roleType}_${key}`,
                    description: isConfigured ? 'Currently configured - select to update' : 'Not configured',
                    emoji: isConfigured ? SETUP_EMOJIS.SUCCESS : SETUP_EMOJIS.ERROR
                };
            });

            if (options.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No role options found for ${roleType}. This might be a configuration error.`,
                    embeds: [],
                    components: []
                });
            }

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_role_to_assign')
                .setPlaceholder('Choose a role to assign...')
                .addOptions(options);

            const row1 = new ActionRowBuilder().addComponents(selectMenu);

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row1, row2]
            });

            this.updateSession(interaction.user.id, {
                step: `${roleType}_role_assignment`,
                currentRoleType: roleType
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing existing role selection:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    getExistingRolesByType(config, roleType) {
        if (!config?.dungeonAlert) return [];

        switch (roleType) {
            case 'rank':
                return Object.entries(config.dungeonAlert.dungeonRoles || {})
                    .filter(([key]) => ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'].includes(key))
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'world':
                return Object.entries(config.dungeonAlert.worldRoles || {})
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'island':
                return Object.entries(config.dungeonAlert.islandRoles || {})
                    .map(([key, roleId]) => ({ key, roleId }));
            case 'special':
                return Object.entries(config.dungeonAlert.dungeonRoles || {})
                    .filter(([key]) => ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(key))
                    .map(([key, roleId]) => ({ key, roleId }));
            default:
                return [];
        }
    },

    getRoleTypeDisplayName(roleType) {
        const names = {
            rank: 'Rank Roles',
            world: 'World Roles',
            island: 'Island Roles',
            special: 'Special Roles'
        };
        return names[roleType] || 'Roles';
    },

    getMaxRolesForType(roleType) {
        switch (roleType) {
            case 'rank': return 9; // E, D, C, B, A, S, SS, G, N
            case 'world': return 2; // World 1, World 2
            case 'special': return 3; // DUNGEON_PING, RED_DUNGEON, DOUBLE_DUNGEON
            case 'island': return 12; // All islands
            default: return 1;
        }
    },

    async handleRoleSelection(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session?.currentRoleType) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} Invalid session state. Please restart the setup.`,
                    embeds: [],
                    components: []
                });
            }

            const selectedRoleIds = interaction.values;
            const roleType = session.currentRoleType;
            const guild = interaction.guild;

            // Validate selected roles exist
            const validRoles = [];
            for (const roleId of selectedRoleIds) {
                const role = guild.roles.cache.get(roleId);
                if (role) {
                    validRoles.push(role);
                }
            }

            if (validRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} None of the selected roles were found. Please try again.`,
                    embeds: [],
                    components: []
                });
            }

            // Show role mapping interface
            await this.showRoleMapping(interaction, validRoles, roleType);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role selection:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleMapping(interaction, selectedRoles, roleType) {
        try {
            const roleKeys = this.getRoleKeysForType(roleType);

            if (selectedRoles.length === 1 && roleKeys.length === 1) {
                // Direct mapping for single role types
                await this.assignRoleMapping(interaction, { [roleKeys[0]]: selectedRoles[0].id }, roleType);
                return;
            }

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.MAP} Map ${this.getRoleTypeDisplayName(roleType)}`)
                .setDescription('Assign the selected roles to specific functions:')
                .addFields([
                    {
                        name: 'Selected Roles',
                        value: selectedRoles.map(role => `<@&${role.id}>`).join(', '),
                        inline: false
                    },
                    {
                        name: 'Available Functions',
                        value: roleKeys.map(key => `**${key}**: ${this.getRoleKeyDescription(key, roleType)}`).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            // Create select menus for each role key
            const components = [];
            for (let i = 0; i < roleKeys.length; i += 5) { // Max 5 per row
                const chunk = roleKeys.slice(i, i + 5);
                const row = new ActionRowBuilder();

                for (const key of chunk) {
                    const selectMenu = new StringSelectMenuBuilder()
                        .setCustomId(`map_role_${roleType}_${key}`)
                        .setPlaceholder(`Select role for ${key}`)
                        .addOptions([
                            { label: 'None', value: 'none', description: 'Skip this role assignment' },
                            ...selectedRoles.map(role => ({
                                label: role.name.length > 100 ? role.name.substring(0, 97) + '...' : role.name,
                                value: role.id,
                                description: `Members: ${role.members.size}`
                            }))
                        ]);

                    row.addComponents(selectMenu);
                    if (row.components.length >= 1) break; // One select menu per row for better UX
                }
                components.push(row);
            }

            // Add control buttons
            const controlRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_role_mapping')
                        .setLabel(`${SETUP_EMOJIS.CONFIRM_MAPPING} Confirm Mapping`)
                        .setStyle(ButtonStyle.Success),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            components.push(controlRow);

            await interaction.update({
                embeds: [embed],
                components: components
            });

            this.updateSession(interaction.user.id, {
                step: 'role_mapping',
                selectedRoles: selectedRoles.map(r => ({ id: r.id, name: r.name })),
                roleMapping: {},
                roleType
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing role mapping:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    getRoleKeysForType(roleType) {
        switch (roleType) {
            case 'rank':
                return ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'];
            case 'world':
                return ['1', '2'];
            case 'special':
                return ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
            case 'island':
                return ['Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town',
                    'Lucky Kingdom', 'Nipon City', 'Mori Town', 'Dragon City',
                    'XZ City', 'Kindama City', 'Hunters City', 'Nen City', 'Hurricane Town', 'Cursed High'];
            default:
                return [];
        }
    },

    getRoleKeyDescription(key, roleType) {
        switch (roleType) {
            case 'rank':
                return `${key} rank dungeon alerts`;
            case 'world':
                return `World ${key} dungeon alerts`;
            case 'special':
                if (key === 'DUNGEON_PING') return 'General dungeon alerts';
                if (key === 'RED_DUNGEON') return 'Red gate alerts';
                if (key === 'DOUBLE_DUNGEON') return 'Double dungeon alerts';
                return key;
            case 'island':
                return `${key} island alerts`;
            default:
                return key;
        }
    },

    getRoleKeyDisplayName(key, roleType) {
        switch (roleType) {
            case 'rank':
                return `${key} Dungeon Ping`;
            case 'world':
                return `World ${key} Ping`;
            case 'special':
                if (key === 'DUNGEON_PING') return 'General Dungeon Ping';
                if (key === 'RED_DUNGEON') return 'Red Gate Ping';
                if (key === 'DOUBLE_DUNGEON') return 'Double Dungeon Ping';
                return key;
            case 'island':
                return `${key} Ping`;
            default:
                return key;
        }
    },

    async showRoleManagement(interaction) {
        try {
            const config = await configService.getServerConfig(interaction.guild.id);
            if (!config?.dungeonAlert) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No dungeon alert configuration found. Please set up the basic configuration first.`,
                    embeds: [],
                    components: []
                });
            }

            // Validate existing roles
            const { valid, invalid } = await this.validateRolesInDatabase(interaction.guild);

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.ROLE_MANAGEMENT} Role Management`)
                .setDescription('Manage your existing ping role configuration:')
                .setTimestamp();

            if (valid.length > 0) {
                embed.addFields([
                    {
                        name: `${SETUP_EMOJIS.VALID_ROLES} Valid Roles`,
                        value: valid.map(role => `<@&${role.roleId}> (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (invalid.length > 0) {
                embed.addFields([
                    {
                        name: `${SETUP_EMOJIS.INVALID_ROLES} Invalid/Missing Roles`,
                        value: invalid.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (valid.length === 0 && invalid.length === 0) {
                embed.addFields([
                    {
                        name: 'No Roles Configured',
                        value: 'No ping roles are currently configured for this server.',
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('unset_roles')
                        .setLabel(`${SETUP_EMOJIS.REMOVE_ROLES} Remove Roles`)
                        .setStyle(ButtonStyle.Danger)
                        .setDisabled(valid.length === 0 && invalid.length === 0),
                    new ButtonBuilder()
                        .setCustomId('cleanup_invalid_roles')
                        .setLabel(`${SETUP_EMOJIS.CLEAN_INVALID} Clean Invalid`)
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(invalid.length === 0),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

            this.updateSession(interaction.user.id, {
                step: 'role_management',
                validRoles: valid,
                invalidRoles: invalid
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing role management:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleUnsetting(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const validRoles = session?.validRoles || [];
            const invalidRoles = session?.invalidRoles || [];

            if (validRoles.length === 0 && invalidRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No roles to remove.`,
                    embeds: [],
                    components: []
                });
            }

            const allRoles = [...validRoles, ...invalidRoles];
            const options = allRoles.map(role => ({
                label: role.key,
                value: `${role.key}:${role.roleId}`,
                description: validRoles.includes(role) ? 'Valid role' : 'Invalid/missing role'
            }));

            const embed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle(`${SETUP_EMOJIS.REMOVE_ROLES} Remove Ping Roles`)
                .setDescription('Select which ping roles to remove from the bot configuration:')
                .addFields([
                    {
                        name: 'Note',
                        value: 'This will only remove the roles from the bot configuration. The actual Discord roles will remain unless you choose to delete them.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_roles_to_unset')
                .setPlaceholder('Choose roles to remove...')
                .setMinValues(1)
                .setMaxValues(Math.min(options.length, 25))
                .addOptions(options);

            const row1 = new ActionRowBuilder().addComponents(selectMenu);

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('unset_all_roles')
                        .setLabel(`${SETUP_EMOJIS.REMOVE_ALL} Remove All`)
                        .setStyle(ButtonStyle.Danger),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row1, row2]
            });

            this.updateSession(interaction.user.id, { step: 'role_unsetting' });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing role unsetting:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleUnsetting(interaction) {
        try {
            const isSelectMenu = interaction.isStringSelectMenu();
            const isButton = interaction.isButton();

            let rolesToRemove = [];

            if (isSelectMenu && interaction.customId === 'select_roles_to_unset') {
                rolesToRemove = interaction.values.map(value => {
                    const [key, roleId] = value.split(':');
                    return { key, roleId };
                });
            } else if (isButton && interaction.customId === 'unset_all_roles') {
                const session = this.getSession(interaction.user.id);
                rolesToRemove = [...(session?.validRoles || []), ...(session?.invalidRoles || [])];
            }

            if (rolesToRemove.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No roles selected for removal.`,
                    embeds: [],
                    components: []
                });
            }

            // Show confirmation with deletion options
            await this.showRoleRemovalConfirmation(interaction, rolesToRemove);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role unsetting:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async showRoleRemovalConfirmation(interaction, rolesToRemove) {
        try {
            const validRoles = [];
            const invalidRoles = [];

            // Separate valid and invalid roles
            for (const role of rolesToRemove) {
                if (await this.validateRoleExists(interaction.guild, role.roleId)) {
                    validRoles.push(role);
                } else {
                    invalidRoles.push(role);
                }
            }

            const embed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle(`${SETUP_EMOJIS.CONFIRM_REMOVAL} Confirm Role Removal`)
                .setDescription('Choose how to handle the selected roles:')
                .setTimestamp();

            if (validRoles.length > 0) {
                embed.addFields([
                    {
                        name: `${SETUP_EMOJIS.VALID_ROLES} Valid Roles to Remove`,
                        value: validRoles.map(role => `<@&${role.roleId}> (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            if (invalidRoles.length > 0) {
                embed.addFields([
                    {
                        name: `${SETUP_EMOJIS.INVALID_ROLES} Invalid Roles to Clean`,
                        value: invalidRoles.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('confirm_remove_config_only')
                        .setLabel(`${SETUP_EMOJIS.REMOVE_CONFIG_ONLY} Remove from Config Only`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('confirm_remove_and_delete')
                        .setLabel(`${SETUP_EMOJIS.REMOVE_AND_DELETE} Remove & Delete Discord Roles`)
                        .setStyle(ButtonStyle.Danger)
                        .setDisabled(validRoles.length === 0),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.CANCEL} Cancel`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row]
            });

            this.updateSession(interaction.user.id, {
                step: 'role_removal_confirmation',
                rolesToRemove,
                validRolesToRemove: validRoles,
                invalidRolesToRemove: invalidRoles
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing role removal confirmation:`), error);
            await interaction.update({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleDeletion(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const deleteDiscordRoles = interaction.customId === 'confirm_remove_and_delete';
            const rolesToRemove = session?.rolesToRemove || [];

            if (rolesToRemove.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No roles to remove.`,
                    embeds: [],
                    components: []
                });
            }

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle(`${SETUP_EMOJIS.PROGRESS} Processing Role Removal`)
                .setDescription('Please wait while I process the role removal...')
                .addFields([
                    { name: '📍 Action', value: deleteDiscordRoles ? 'Remove from config & delete from Discord' : 'Remove from config only', inline: true },
                    { name: '⚙️ Status', value: '🔄 Removing from configuration...', inline: true }
                ])
                .setTimestamp();

            await interaction.update({
                embeds: [progressEmbed],
                components: []
            });

            // Remove from database configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            if (config?.dungeonAlert) {
                for (const role of rolesToRemove) {
                    // Determine which role category to update
                    if (['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N', 'DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(role.key)) {
                        delete config.dungeonAlert.dungeonRoles[role.key];
                    } else if (['1', '2'].includes(role.key)) {
                        delete config.dungeonAlert.worldRoles[role.key];
                    } else {
                        delete config.dungeonAlert.islandRoles[role.key];
                    }
                }

                await configService.saveServerConfig(interaction.guild.id, config);
            }

            const results = {
                removedFromConfig: rolesToRemove.length,
                deletedFromDiscord: 0,
                failedDeletions: []
            };

            // Delete Discord roles if requested
            if (deleteDiscordRoles) {
                const validRoles = session?.validRolesToRemove || [];

                // Update progress to show deletion phase
                progressEmbed.setFields(
                    { name: '📍 Action', value: 'Remove from config & delete from Discord', inline: true },
                    { name: '⚙️ Status', value: `🔄 Deleting Discord roles... (0/${validRoles.length})`, inline: true }
                );
                await interaction.editReply({ embeds: [progressEmbed] });

                let roleCount = 0;
                for (const role of validRoles) {
                    try {
                        roleCount++;
                        const roleId = typeof role.roleId === 'string' ? role.roleId : String(role.roleId);
                        const discordRole = interaction.guild.roles.cache.get(roleId);

                        if (discordRole) {
                            // Update progress with current role
                            progressEmbed.setFields(
                                { name: '📍 Action', value: 'Remove from config & delete from Discord', inline: true },
                                { name: '⚙️ Status', value: `🔄 Deleting Discord roles... (${roleCount}/${validRoles.length})\nCurrent: **${discordRole.name}**`, inline: true }
                            );
                            await interaction.editReply({ embeds: [progressEmbed] });

                            await discordRole.delete('Removed via setup command');
                            results.deletedFromDiscord++;
                            console.log(chalk.green(`${SETUP_EMOJIS.SUCCESS} Deleted role: ${discordRole.name} (${roleId})`));
                        } else {
                            console.log(chalk.yellow(`${SETUP_EMOJIS.WARNING} Role not found in cache: ${roleId} (${role.key})`));
                        }
                    } catch (error) {
                        console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to delete role ${role.key} (${role.roleId}):`), error);
                        results.failedDeletions.push(role.key);
                    }
                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            }

            // Show completion message
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.COMPLETE} Role Removal Complete`)
                .setDescription('Role removal has been completed successfully!')
                .addFields([
                    {
                        name: 'Summary',
                        value: [
                            `${SETUP_EMOJIS.REMOVE_CONFIG_ONLY} Removed from config: ${results.removedFromConfig}`,
                            deleteDiscordRoles ? `${SETUP_EMOJIS.DELETED_COUNT} Deleted from Discord: ${results.deletedFromDiscord}` : `${SETUP_EMOJIS.REMOVE_CONFIG_ONLY} Discord roles kept`,
                            results.failedDeletions.length > 0 ? `${SETUP_EMOJIS.FAILED_COUNT} Failed deletions: ${results.failedDeletions.join(', ')}` : ''
                        ].filter(Boolean).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role deletion:`), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async cleanupInvalidRoles(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const invalidRoles = session?.invalidRoles || [];

            if (invalidRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No invalid roles to clean up.`,
                    embeds: [],
                    components: []
                });
            }

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle(`${SETUP_EMOJIS.PROGRESS} Cleaning Invalid Roles`)
                .setDescription('Please wait while I clean up invalid roles from the configuration...')
                .addFields([
                    { name: '📍 Action', value: 'Remove invalid roles from configuration', inline: true },
                    { name: '⚙️ Status', value: '🔄 Processing...', inline: true }
                ])
                .setTimestamp();

            await interaction.update({
                embeds: [progressEmbed],
                components: []
            });

            // Remove invalid roles from database
            const config = await configService.getServerConfig(interaction.guild.id);
            if (config?.dungeonAlert) {
                for (const role of invalidRoles) {
                    if (['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N', 'DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'].includes(role.key)) {
                        delete config.dungeonAlert.dungeonRoles[role.key];
                    } else if (['1', '2'].includes(role.key)) {
                        delete config.dungeonAlert.worldRoles[role.key];
                    } else {
                        delete config.dungeonAlert.islandRoles[role.key];
                    }
                }

                await configService.saveServerConfig(interaction.guild.id, config);
            }

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.CLEANUP_COMPLETE} Cleanup Complete`)
                .setDescription(`Successfully cleaned up ${invalidRoles.length} invalid role(s) from the configuration.`)
                .addFields([
                    {
                        name: 'Cleaned Roles',
                        value: invalidRoles.map(role => `~~${role.roleId}~~ (${role.key})`).join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('manage_existing_roles')
                        .setLabel(`${SETUP_EMOJIS.MANAGE_ROLES} Manage Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error cleaning up invalid roles:`), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async confirmRoleMapping(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session?.roleMapping || !session?.roleType) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} Invalid session state. Please restart the setup.`,
                    embeds: [],
                    components: []
                });
            }

            const roleMapping = session.roleMapping;
            const roleType = session.roleType;

            // Validate mapping has at least one role assigned
            const assignedRoles = Object.values(roleMapping).filter(roleId => roleId && roleId !== 'none');
            if (assignedRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} Please assign at least one role before confirming.`,
                    embeds: [],
                    components: []
                });
            }

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.PROGRESS} Saving Role Configuration`)
                .setDescription('Please wait while I save the role configuration...')
                .addFields([
                    { name: '📍 Role Type', value: this.getRoleTypeDisplayName(roleType), inline: true },
                    { name: '⚙️ Status', value: '🔄 Saving to database...', inline: true }
                ])
                .setTimestamp();

            await interaction.update({
                embeds: [progressEmbed],
                components: []
            });

            // Save to session and immediately persist to MongoDB
            const currentRoles = {
                dungeonRoles: session.dungeonRoles || {},
                worldRoles: session.worldRoles || {},
                islandRoles: session.islandRoles || {}
            };

            // Update the appropriate role category
            for (const [key, roleId] of Object.entries(roleMapping)) {
                if (roleId && roleId !== 'none') {
                    if (roleType === 'world') {
                        currentRoles.worldRoles[key] = roleId;
                    } else if (roleType === 'island') {
                        currentRoles.islandRoles[key] = roleId;
                    } else {
                        currentRoles.dungeonRoles[key] = roleId;
                    }
                }
            }

            this.updateSession(interaction.user.id, currentRoles);

            // Persist to MongoDB immediately
            const config = await configService.getServerConfig(interaction.guild.id) || {
                serverId: interaction.guild.id,
                name: interaction.guild.name,
                dungeonAlert: {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                }
            };
            if (!config.dungeonAlert) config.dungeonAlert = { enabled: false, dungeonRoles: {}, worldRoles: {}, islandRoles: {} };
            config.dungeonAlert.dungeonRoles = currentRoles.dungeonRoles;
            config.dungeonAlert.worldRoles = currentRoles.worldRoles;
            config.dungeonAlert.islandRoles = currentRoles.islandRoles;
            await configService.saveServerConfig(interaction.guild.id, config);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} ${this.getRoleTypeDisplayName(roleType)} Configured`)
                .setDescription(`Successfully configured ${assignedRoles.length} ${roleType} role(s)!`)
                .addFields([
                    {
                        name: 'Configured Roles',
                        value: Object.entries(roleMapping)
                            .filter(([, roleId]) => roleId && roleId !== 'none')
                            .map(([key, roleId]) => `**${key}**: <@&${roleId}>`)
                            .join('\n') || 'None',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error confirming role mapping:`), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleRoleMapping(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            if (!session) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} No active setup session found.`,
                    ephemeral: true
                });
            }

            // Parse the custom ID to get role key
            const parts = interaction.customId.split('_');
            if (parts.length < 4) return;

            const roleKey = parts.slice(3).join('_');
            const selectedRoleId = interaction.values[0];

            // Update session with the mapping
            if (!session.roleMapping) {
                session.roleMapping = {};
            }
            session.roleMapping[roleKey] = selectedRoleId;

            this.updateSession(interaction.user.id, session);

            // Acknowledge the selection
            await interaction.reply({
                content: `${SETUP_EMOJIS.SUCCESS} Mapped **${roleKey}** to ${selectedRoleId === 'none' ? 'None' : `<@&${selectedRoleId}>`}`,
                ephemeral: true
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role mapping:`), error);
            await interaction.reply({
                content: this.getDetailedErrorMessage(error),
                ephemeral: true
            });
        }
    },

    async assignRoleMapping(interaction, roleMapping, roleType) {
        try {
            await interaction.update({
                content: `${SETUP_EMOJIS.PROCESSING} Saving role configuration...`,
                embeds: [],
                components: []
            });

            // Save to session for later database update
            const session = this.getSession(interaction.user.id);
            const currentRoles = {
                dungeonRoles: session?.dungeonRoles || {},
                worldRoles: session?.worldRoles || {},
                islandRoles: session?.islandRoles || {}
            };

            // Update the appropriate role category
            for (const [key, roleId] of Object.entries(roleMapping)) {
                if (roleId && roleId !== 'none') {
                    if (roleType === 'world') {
                        currentRoles.worldRoles[key] = roleId;
                    } else if (roleType === 'island') {
                        currentRoles.islandRoles[key] = roleId;
                    } else {
                        currentRoles.dungeonRoles[key] = roleId;
                    }
                }
            }

            this.updateSession(interaction.user.id, currentRoles);

            const assignedRoles = Object.values(roleMapping).filter(roleId => roleId && roleId !== 'none');

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} ${this.getRoleTypeDisplayName(roleType)} Configured`)
                .setDescription(`Successfully configured ${assignedRoles.length} ${roleType} role(s)!`)
                .addFields([
                    {
                        name: 'Configured Roles',
                        value: Object.entries(roleMapping)
                            .filter(([, roleId]) => roleId && roleId !== 'none')
                            .map(([key, roleId]) => `**${key}**: <@&${roleId}>`)
                            .join('\n') || 'None',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({
                embeds: [embed],
                components: [row]
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error assigning role mapping:`), error);
            await interaction.editReply({
                content: this.getDetailedErrorMessage(error),
                embeds: [],
                components: []
            });
        }
    },

    async handleChannelSelection(interaction) {
        try {
            const selectedChannelId = interaction.values[0];
            const channel = interaction.guild.channels.cache.get(selectedChannelId);

            if (!channel) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.CHANNEL_NOT_FOUND} Selected channel not found. Please try again.`,
                    ephemeral: true
                });
            }

            // Get session to determine which feature we're configuring
            const session = this.getSession(interaction.user.id);
            const feature = session?.feature || 'auto_dungeons'; // Default to dungeons for backward compatibility
            const isWorldBoss = feature === 'auto_worldboss';

            // Save channel to database immediately
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: { enabled: false },
                    worldBossAlert: { enabled: false },
                    infernalAlert: { enabled: false }
                };
            }

            if (isWorldBoss) {
                // Configure world boss alert
                if (!config.worldBossAlert) {
                    config.worldBossAlert = { enabled: false };
                }
                config.worldBossAlert.targetChannelId = selectedChannelId;
            } else {
                // Configure dungeon alert
                if (!config.dungeonAlert) {
                    config.dungeonAlert = {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    };
                }
                config.dungeonAlert.targetChannelId = selectedChannelId;
            }

            await configService.saveServerConfig(interaction.guild.id, config);

            // Update session
            this.updateSession(interaction.user.id, {
                selectedChannelId: selectedChannelId,
                step: 'channel_selected'
            });

            // Show success message with appropriate feature-specific content
            const featureDisplayName = isWorldBoss ? 'world boss alerts' : 'dungeon alerts';
            const setupRolesCustomId = isWorldBoss ? 'worldboss_setup_roles' : 'dungeons_setup_roles';
            const backCustomId = isWorldBoss ? 'setup_auto_worldboss' : 'setup_auto_dungeons';

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.CHANNEL_SELECTED} Channel Successfully Configured`)
                .setDescription(`Successfully selected and saved ${channel} for ${featureDisplayName}!`)
                .addFields([
                    {
                        name: 'Configuration Status',
                        value: `${SETUP_EMOJIS.SUCCESS} Channel has been automatically saved to the database`,
                        inline: false
                    },
                    {
                        name: 'Next Steps',
                        value: 'You can now set up ping roles or go back to the main setup.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(setupRolesCustomId)
                        .setLabel(`${SETUP_EMOJIS.SETUP_PING_ROLES} Setup Ping Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId(backCustomId)
                        .setLabel(`${SETUP_EMOJIS.BACK_TO_MAIN} Back to Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling channel selection:`), error);
            await interaction.reply({
                content: this.getDetailedErrorMessage(error),
                ephemeral: true
            });
        }
    },

    async handleRoleAssignmentSelection(interaction) {
        try {
            const selectedValue = interaction.values[0];
            const parts = selectedValue.split('_');

            if (parts.length < 3 || parts[0] !== 'assign') {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.INVALID_FORMAT} Invalid selection. Please try again.`,
                    ephemeral: true
                });
            }

            const roleType = parts[1];
            const roleKey = parts.slice(2).join('_');
            const displayName = this.getRoleKeyDisplayName(roleKey, roleType);

            // Update session to track what we're assigning
            this.updateSession(interaction.user.id, {
                pendingRoleAssignment: {
                    roleType,
                    roleKey,
                    displayName,
                    channelId: interaction.channel.id,
                    userId: interaction.user.id
                }
            });

            // Create a collector to wait for the user's role input
            const filter = (message) => message.author.id === interaction.user.id;
            const collector = interaction.channel.createMessageCollector({
                filter,
                time: 60000, // 1 minute timeout
                max: 1
            });

            await interaction.reply({
                content: `${SETUP_EMOJIS.ROLE_INPUT} **Assigning ${displayName}**\n\nPlease provide the role information in one of these formats:\n• **Role mention**: @RoleName\n• **Role ID**: 123456789012345678\n\n*You have 60 seconds to respond.*`,
                ephemeral: true
            });

            collector.on('collect', async (message) => {
                try {
                    await this.processRoleInput(message, roleType, roleKey);
                } catch (error) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing role input:`), error);
                    await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while processing your role input. Please try again.`);
                }
            });

            collector.on('end', (collected) => {
                if (collected.size === 0) {
                    interaction.followUp({
                        content: `${SETUP_EMOJIS.TIMEOUT_WARNING} Role assignment timed out. Please try again.`,
                        ephemeral: true
                    }).catch(console.error);
                }
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error handling role assignment selection:`), error);
            await interaction.reply({
                content: this.getDetailedErrorMessage(error),
                ephemeral: true
            });
        }
    },

    async processRoleInput(message, roleType, roleKey) {
        try {
            const roleInput = message.content.trim();
            let roleId = null;

            // Parse role mention or ID
            const mentionMatch = roleInput.match(/^<@&(\d+)>$/);
            if (mentionMatch) {
                roleId = mentionMatch[1];
            }
            // Check if it's just a numeric ID
            else if (/^\d+$/.test(roleInput)) {
                roleId = roleInput;
            }
            else {
                return await message.reply(`${SETUP_EMOJIS.INVALID_FORMAT} Invalid format. Please provide either a role mention (@RoleName) or a numeric role ID.`);
            }

            // Validate the role exists
            const role = message.guild.roles.cache.get(roleId);
            if (!role) {
                return await message.reply(`${SETUP_EMOJIS.ROLE_NOT_FOUND} Role not found. Please make sure the role exists in this server and try again.`);
            }

            // Save the role assignment
            await this.saveRoleAssignment(message, roleType, roleKey, roleId);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing role input:`), error);
            await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while processing your role input. Please try again.`);
        }
    },

    async saveRoleAssignment(message, roleType, roleKey, roleId) {
        try {
            // Delete the user's input message
            try {
                await message.delete();
            } catch (error) {
                console.log('Could not delete user message (may lack permissions)');
            }

            // Get current configuration
            const config = await configService.getServerConfig(message.guild.id) || {
                serverId: message.guild.id,
                name: message.guild.name,
                dungeonAlert: {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                }
            };

            // Update the appropriate role category
            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {}
                };
            }

            if (roleType === 'world') {
                config.dungeonAlert.worldRoles[roleKey] = roleId;
            } else if (roleType === 'island') {
                config.dungeonAlert.islandRoles[roleKey] = roleId;
            } else {
                // rank and special roles go in dungeonRoles
                config.dungeonAlert.dungeonRoles[roleKey] = roleId;
            }

            // Save to database
            await configService.saveServerConfig(message.guild.id, config);

            // Update session with the new role
            const session = this.getSession(message.author.id);
            if (session) {
                if (!session.dungeonRoles) session.dungeonRoles = {};
                if (!session.worldRoles) session.worldRoles = {};
                if (!session.islandRoles) session.islandRoles = {};

                if (roleType === 'world') {
                    session.worldRoles[roleKey] = roleId;
                } else if (roleType === 'island') {
                    session.islandRoles[roleKey] = roleId;
                } else {
                    session.dungeonRoles[roleKey] = roleId;
                }

                this.updateSession(message.author.id, session);
            }

            // Find the original setup message and update it immediately
            const channel = message.channel;
            const lastMessages = await channel.messages.fetch({ limit: 10 });

            // Look for the setup message to update
            for (const [, msg] of lastMessages) {
                if (msg.author.id === message.client.user.id &&
                    msg.embeds.length > 0 &&
                    msg.embeds[0].title?.includes('Assign Existing')) {

                    // Create a mock interaction to refresh the interface
                    const mockInteraction = {
                        guild: message.guild,
                        user: message.author,
                        update: async (options) => {
                            await msg.edit(options);
                        }
                    };

                    await this.showExistingRoleSelection(mockInteraction, roleType);
                    break;
                }
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error saving role assignment:`), error);
            await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while saving the role assignment. Please try again.`);
        }
    },

    async processWorldBossRoleInput(message, roleType, roleKey) {
        try {
            const roleInput = message.content.trim();
            let roleId = null;

            // Parse role mention or ID
            const mentionMatch = roleInput.match(/^<@&(\d+)>$/);
            if (mentionMatch) {
                roleId = mentionMatch[1];
            }
            // Check if it's just a numeric ID
            else if (/^\d+$/.test(roleInput)) {
                roleId = roleInput;
            }
            else {
                return await message.channel.send(`${SETUP_EMOJIS.INVALID_FORMAT} Invalid format. Please provide either a role mention (@RoleName) or a numeric role ID.`);
            }

            // Validate the role exists
            const role = message.guild.roles.cache.get(roleId);
            if (!role) {
                return await message.channel.send(`${SETUP_EMOJIS.ROLE_NOT_FOUND} Role not found. Please make sure the role exists in this server and try again.`);
            }

            // Save the world boss role assignment
            await this.saveWorldBossRoleAssignment(message, roleType, roleKey, roleId);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing world boss role input:`), error);
            await message.channel.send(`${SETUP_EMOJIS.ERROR} An error occurred while processing your role input. Please try again.`);
        }
    },

    async saveWorldBossRoleAssignment(message, roleType, roleKey, roleId) {
        try {
            // Delete the user's input message
            try {
                await message.delete();
            } catch (error) {
                console.log('Could not delete user message (may lack permissions)');
            }

            // Get current configuration
            const config = await configService.getServerConfig(message.guild.id) || {
                serverId: message.guild.id,
                name: message.guild.name,
                dungeonAlert: { enabled: false },
                worldBossAlert: { enabled: false },
                infernalAlert: { enabled: false }
            };

            // Initialize worldBossAlert if it doesn't exist
            if (!config.worldBossAlert) {
                config.worldBossAlert = { enabled: false };
            }

            // Initialize worldBossRoles if it doesn't exist
            if (!config.worldBossAlert.worldBossRoles) {
                config.worldBossAlert.worldBossRoles = {};
            }

            // Assign the role
            config.worldBossAlert.worldBossRoles[roleKey] = roleId;

            // Save configuration
            await configService.saveServerConfig(message.guild.id, config);

            const role = message.guild.roles.cache.get(roleId);
            const displayName = roleKey === 'general' ? 'General World Boss Role' :
                roleKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) + ' Boss Role';

            // Send success message
            await message.channel.send({
                embeds: [
                    new EmbedBuilder()
                        .setColor('#00ff00')
                        .setTitle(`${SETUP_EMOJIS.SUCCESS} Role Assigned Successfully`)
                        .setDescription(`**${displayName}** has been assigned to ${role}.`)
                        .addFields([
                            {
                                name: 'Role',
                                value: `<@&${roleId}>`,
                                inline: true
                            },
                            {
                                name: 'Type',
                                value: displayName,
                                inline: true
                            }
                        ])
                        .setTimestamp()
                ]
            });

            console.log(chalk.green(`${SETUP_EMOJIS.SUCCESS} World boss role assigned: ${displayName} -> ${role.name} (${roleId})`));

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error saving world boss role assignment:`), error);
            await message.channel.send(`${SETUP_EMOJIS.ERROR} An error occurred while saving the role assignment. Please try again.`);
        }
    },

    async processChannelInput(message, originalInteraction) {
        try {
            const channelInput = message.content.trim();
            let channelId = null;

            // Parse channel mention or ID
            const mentionMatch = channelInput.match(/^<#(\d+)>$/);
            if (mentionMatch) {
                channelId = mentionMatch[1];
            }
            // Check if it's just a numeric ID
            else if (/^\d+$/.test(channelInput)) {
                channelId = channelInput;
            }
            else {
                return await message.channel.send(`${SETUP_EMOJIS.INVALID_FORMAT} Invalid format. Please provide either a channel mention (#channel-name) or a numeric channel ID.`);
            }

            // Validate the channel exists and is a text channel
            const channel = message.guild.channels.cache.get(channelId);
            if (!channel) {
                return await message.channel.send(`${SETUP_EMOJIS.CHANNEL_NOT_FOUND} Channel not found. Please make sure the channel exists in this server and try again.`);
            }

            // Get session to determine feature type
            const session = this.getSession(message.author.id);
            const feature = session?.feature || 'dungeons';
            const isWorldBoss = feature === 'worldboss';
            const alertType = isWorldBoss ? 'world boss alerts' : 'dungeon alerts';

            if (channel.type !== ChannelType.GuildText) {
                return await message.channel.send(`${SETUP_EMOJIS.INVALID_CHANNEL_TYPE} Invalid channel type. Please provide a text channel for ${alertType}.`);
            }

            // Save the channel assignment and immediately update the ephemeral message
            await this.saveChannelAssignment(message, channelId, channel.name, originalInteraction);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing channel input:`), error);
            await message.channel.send(`${SETUP_EMOJIS.ERROR} An error occurred while processing your channel input. Please try again.`);
        }
    },

    async saveChannelAssignment(message, channelId, channelName, originalInteraction) {
        try {
            // Delete the user's input message
            try {
                await message.delete();
            } catch (error) {
                console.log('Could not delete user message (may lack permissions)');
            }

            // Get session to determine feature type
            const session = this.getSession(message.author.id);
            const feature = session?.feature || 'dungeons';
            const isWorldBoss = feature === 'worldboss';

            // Save channel to database immediately
            let config = await configService.getServerConfig(message.guild.id);
            if (!config) {
                config = {
                    serverId: message.guild.id,
                    name: message.guild.name,
                    dungeonAlert: { enabled: false },
                    worldBossAlert: { enabled: false },
                    infernalAlert: { enabled: false }
                };
            }

            if (isWorldBoss) {
                // Configure world boss alert
                if (!config.worldBossAlert) {
                    config.worldBossAlert = { enabled: false };
                }
                config.worldBossAlert.targetChannelId = channelId;
            } else {
                // Configure dungeon alert
                if (!config.dungeonAlert) {
                    config.dungeonAlert = {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {}
                    };
                }
                config.dungeonAlert.targetChannelId = channelId;
            }

            await configService.saveServerConfig(message.guild.id, config);

            // Clear session selectedChannelId since it's now saved to database
            this.updateSession(message.author.id, {
                selectedChannelId: null,
                selectedChannelName: null,
                step: 'channel_configured',
                feature: feature
            });

            console.log(chalk.green(`✅ Channel configuration saved for user ${message.author.id}, immediately updating ephemeral message`));

            // Feature-specific content
            const featureDisplayName = isWorldBoss ? 'world boss alerts' : 'dungeon alerts';
            const featureTitle = isWorldBoss ? 'World Boss' : 'Dungeon';
            const setupRolesCustomId = isWorldBoss ? 'worldboss_setup_roles' : 'dungeons_setup_roles';
            const backToSetupCustomId = isWorldBoss ? 'setup_auto_worldboss' : 'setup_auto_dungeons';
            const backToSetupLabel = isWorldBoss ? 'Back to Auto World Boss' : 'Back to Auto Dungeons';
            const nextStepsText = isWorldBoss
                ? 'You can now set up ping roles for world boss alerts or go back to the main setup.'
                : 'You can now set up ping roles for different dungeon types or go back to the main setup.';

            // Immediately update the original ephemeral message with success
            const successEmbed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} Channel Successfully Configured`)
                .setDescription(`Successfully configured <#${channelId}> as the ${featureDisplayName} channel!`)
                .addFields([
                    {
                        name: 'Selected Channel',
                        value: `<#${channelId}> ${channelName ? `(${channelName})` : ''}`,
                        inline: false
                    },
                    {
                        name: 'Status',
                        value: `${SETUP_EMOJIS.SUCCESS} Channel has been saved to the database and is ready for use`,
                        inline: false
                    },
                    {
                        name: 'What\'s Next?',
                        value: nextStepsText,
                        inline: false
                    }
                ])
                .setTimestamp();

            const successRow = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(backToSetupCustomId)
                        .setLabel(`${SETUP_EMOJIS.BACK} ${backToSetupLabel}`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId(setupRolesCustomId)
                        .setLabel(`${SETUP_EMOJIS.SETUP_PING_ROLES} Setup Ping Roles`)
                        .setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK_TO_MAIN} Back to Main Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            // Update the original ephemeral message immediately
            await originalInteraction.editReply({
                embeds: [successEmbed],
                components: [successRow]
            });

            console.log(chalk.green(`✅ Ephemeral message updated successfully with channel configuration`));

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error saving channel assignment:`), error);
            try {
                const errorMessage = await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while saving the channel assignment. Please try again.`);

                // Auto-delete the error message after 10 seconds
                setTimeout(async () => {
                    try {
                        await errorMessage.delete();
                    } catch (deleteError) {
                        console.log(chalk.yellow(`⚠️ Could not delete error message: ${deleteError.message}`));
                    }
                }, 10000);
            } catch (replyError) {
                console.log(chalk.red(`❌ Could not send error reply: ${replyError.message}`));
            }
        }
    },

    async toggleRoleCategory(interaction, roleType) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);

            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: {
                        enabled: false,
                        dungeonRoles: {},
                        worldRoles: {},
                        islandRoles: {},
                        rankRolesEnabled: true,
                        worldRolesEnabled: true,
                        islandRolesEnabled: true,
                        specialRolesEnabled: true
                    },
                    worldBossAlert: { enabled: false },
                    infernalAlert: { enabled: false }
                };
            }

            if (!config.dungeonAlert) {
                config.dungeonAlert = {
                    enabled: false,
                    dungeonRoles: {},
                    worldRoles: {},
                    islandRoles: {},
                    rankRolesEnabled: true,
                    worldRolesEnabled: true,
                    islandRolesEnabled: true,
                    specialRolesEnabled: true
                };
            }

            // Get current state and toggle it
            const enabledKey = `${roleType}RolesEnabled`;
            const currentState = config.dungeonAlert[enabledKey] !== false; // Default to true
            const newState = !currentState;

            // If disabling and there are existing roles, show confirmation dialog
            if (!newState) {
                const hasRoles = this.hasConfiguredRoles(config, roleType);
                if (hasRoles) {
                    return await this.showDisableRoleConfirmation(interaction, roleType);
                }
            }

            // Update the state
            config.dungeonAlert[enabledKey] = newState;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Refresh the appropriate setup page
            switch (roleType) {
                case 'rank':
                    await this.setupRankRoles(interaction);
                    break;
                case 'world':
                    await this.setupWorldRoles(interaction);
                    break;
                case 'island':
                    await this.setupIslandRoles(interaction);
                    break;
                case 'special':
                    await this.setupSpecialRoles(interaction);
                    break;
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error toggling ${roleType} roles:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to toggle ${roleType} roles. Please try again.`,
                ephemeral: true
            });
        }
    },

    hasConfiguredRoles(config, roleType) {
        switch (roleType) {
            case 'rank':
                const rankKeys = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'];
                return rankKeys.some(key => config.dungeonAlert?.dungeonRoles?.[key]);
            case 'world':
                const worldKeys = ['1', '2'];
                return worldKeys.some(key => config.dungeonAlert?.worldRoles?.[key]);
            case 'island':
                return Object.keys(config.dungeonAlert?.islandRoles || {}).length > 0;
            case 'special':
                const specialKeys = ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
                return specialKeys.some(key => config.dungeonAlert?.dungeonRoles?.[key]);
            default:
                return false;
        }
    },

    async showDisableRoleConfirmation(interaction, roleType) {
        const embed = new EmbedBuilder()
            .setColor('#ff9900')
            .setTitle(`${SETUP_EMOJIS.DISABLE_WARNING} Disable ${this.getRoleTypeDisplayName(roleType)}?`)
            .setDescription(`You have existing ${roleType} roles configured. What would you like to do with them?`)
            .addFields([
                {
                    name: 'Options',
                    value: '• **Keep Roles**: Disable pinging but keep role configurations\n• **Delete Roles**: Remove roles from Discord and database',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`disable_${roleType}_keep`)
                    .setLabel(`${SETUP_EMOJIS.KEEP_ROLES} Keep Roles (Disable Only)`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`disable_${roleType}_delete`)
                    .setLabel(`${SETUP_EMOJIS.DELETE_ROLES} Delete Roles`)
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.CANCEL} Cancel`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, {
            step: 'disable_role_confirmation',
            roleType: roleType
        });
    },

    async handleDisableRoleKeep(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const roleType = session?.roleType;

            if (!roleType) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} Invalid session state. Please restart the setup.`,
                    ephemeral: true
                });
            }

            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);

            // Disable the role category but keep the roles
            const enabledKey = `${roleType}RolesEnabled`;
            config.dungeonAlert[enabledKey] = false;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Refresh the appropriate setup page
            switch (roleType) {
                case 'rank':
                    await this.setupRankRoles(interaction);
                    break;
                case 'world':
                    await this.setupWorldRoles(interaction);
                    break;
                case 'island':
                    await this.setupIslandRoles(interaction);
                    break;
                case 'special':
                    await this.setupSpecialRoles(interaction);
                    break;
            }

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error disabling role category:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to disable role category. Please try again.`,
                ephemeral: true
            });
        }
    },

    async handleDisableRoleDelete(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const roleType = session?.roleType;

            if (!roleType) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} Invalid session state. Please restart the setup.`,
                    ephemeral: true
                });
            }

            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);

            if (!config || !config.dungeonAlert) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No configuration found. Please set up the basic configuration first.`,
                    embeds: [],
                    components: []
                });
            }

            // Get roles to delete
            const rolesToDelete = this.getRolesToDelete(config, roleType);

            // Debug logging
            console.log(chalk.blue(`🔍 Debug - Role type: ${roleType}`));
            console.log(chalk.blue(`🔍 Debug - Roles to delete:`, rolesToDelete));
            console.log(chalk.blue(`🔍 Debug - Roles to delete length: ${rolesToDelete.length}`));
            console.log(chalk.blue(`🔍 Debug - Roles to delete types:`, rolesToDelete.map(r => typeof r)));

            // Additional safety check - filter out any non-string values and ensure they're valid Discord IDs
            const validRoleIds = rolesToDelete.filter(roleId => {
                // Check if it's a string
                if (typeof roleId !== 'string') {
                    console.log(chalk.red(`🚫 Filtering out non-string role ID:`, roleId, `(type: ${typeof roleId})`));
                    return false;
                }

                // Check if it's not empty and not null/undefined strings
                if (roleId.length === 0 || roleId === 'null' || roleId === 'undefined') {
                    console.log(chalk.red(`🚫 Filtering out empty/null role ID: "${roleId}"`));
                    return false;
                }

                // Check if it's a valid Discord ID (numeric string, 17-19 digits)
                if (!/^\d{17,19}$/.test(roleId)) {
                    console.log(chalk.red(`🚫 Filtering out invalid Discord ID format: "${roleId}"`));
                    return false;
                }

                return true;
            });

            console.log(chalk.blue(`🔍 Debug - Valid role IDs after filtering:`, validRoleIds));

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#ff9900')
                .setTitle(`${SETUP_EMOJIS.PROGRESS} Deleting ${this.getRoleTypeDisplayName(roleType)}`)
                .setDescription('Please wait while I delete the roles...')
                .addFields([
                    { name: '📍 Role Category', value: this.getRoleTypeDisplayName(roleType), inline: true },
                    { name: '⚙️ Status', value: `🔄 Deleting roles... (0/${validRoleIds.length})`, inline: true }
                ])
                .setTimestamp();

            await interaction.update({
                embeds: [progressEmbed],
                components: []
            });

            // Delete Discord roles
            let deletedCount = 0;
            let failedCount = 0;
            const failedRoles = [];
            const totalRoles = validRoleIds.length;

            for (const roleId of validRoleIds) {
                try {
                    // Ensure roleId is a string and not an object
                    const cleanRoleId = typeof roleId === 'string' ? roleId : String(roleId);

                    // Additional validation
                    if (!cleanRoleId || cleanRoleId === 'null' || cleanRoleId === 'undefined') {
                        console.log(chalk.yellow(`${SETUP_EMOJIS.WARNING} Skipping invalid role ID: ${cleanRoleId}`));
                        continue;
                    }

                    const role = interaction.guild.roles.cache.get(cleanRoleId);
                    if (role) {
                        // Update progress
                        progressEmbed.setFields(
                            { name: '📍 Role Category', value: this.getRoleTypeDisplayName(roleType), inline: true },
                            { name: '⚙️ Status', value: `🔄 Deleting roles... (${deletedCount + failedCount + 1}/${totalRoles})\nCurrent: **${role.name}**`, inline: true }
                        );
                        await interaction.editReply({ embeds: [progressEmbed] });

                        await role.delete('Disabled via setup command');
                        deletedCount++;
                        console.log(chalk.green(`${SETUP_EMOJIS.SUCCESS} Deleted role: ${role.name} (${cleanRoleId})`));
                    } else {
                        console.log(chalk.yellow(`${SETUP_EMOJIS.WARNING} Role not found in cache: ${cleanRoleId}`));
                    }
                } catch (error) {
                    const cleanRoleId = typeof roleId === 'string' ? roleId : String(roleId);
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to delete role ${cleanRoleId}:`), error);
                    failedCount++;
                    failedRoles.push(cleanRoleId);
                }
                // Small delay to avoid rate limits
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // Update progress to saving configuration
            progressEmbed.setFields(
                { name: '📍 Role Category', value: this.getRoleTypeDisplayName(roleType), inline: true },
                { name: '⚙️ Status', value: '💾 Saving configuration...', inline: true }
            );
            await interaction.editReply({ embeds: [progressEmbed] });

            // Remove roles from database and disable category
            this.removeRolesFromConfig(config, roleType);
            const enabledKey = `${roleType}RolesEnabled`;
            config.dungeonAlert[enabledKey] = false;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Show completion message
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.DISABLED} ${this.getRoleTypeDisplayName(roleType)} Disabled`)
                .setDescription(`Successfully disabled ${roleType} roles and cleaned up Discord roles.`)
                .addFields([
                    {
                        name: 'Summary',
                        value: [
                            `${SETUP_EMOJIS.DELETED_COUNT} Deleted: ${deletedCount} roles`,
                            failedCount > 0 ? `${SETUP_EMOJIS.FAILED_COUNT} Failed: ${failedCount} roles` : `${SETUP_EMOJIS.ALL_SUCCESS_DELETE} All roles deleted successfully`,
                            `${SETUP_EMOJIS.DISABLED_CONFIG} ${this.getRoleTypeDisplayName(roleType)} disabled in configuration`
                        ].join('\n'),
                        inline: false
                    }
                ])
                .setTimestamp();

            if (failedRoles.length > 0) {
                embed.addFields([
                    {
                        name: 'Failed Role IDs',
                        value: failedRoles.join(', '),
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('dungeons_setup_roles')
                        .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Setup More Roles`)
                        .setStyle(ButtonStyle.Primary),
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK_TO_MAIN} Back to Main Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error deleting roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to delete roles. Error: ${error.message}`,
                embeds: [],
                components: []
            });
        }
    },

    getRolesToDelete(config, roleType) {
        const roleIds = [];

        console.log(chalk.blue(`🔍 Debug getRolesToDelete - roleType: ${roleType}`));
        console.log(chalk.blue(`🔍 Debug getRolesToDelete - config.dungeonAlert:`, config.dungeonAlert));

        switch (roleType) {
            case 'rank':
                const rankKeys = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'];
                rankKeys.forEach(key => {
                    const roleId = config.dungeonAlert?.dungeonRoles?.[key];
                    if (roleId && roleId !== null && roleId !== 'null') {
                        // Ensure roleId is a string and valid
                        const id = typeof roleId === 'string' ? roleId : String(roleId);
                        if (id && id !== 'null' && id !== 'undefined') {
                            roleIds.push(id);
                            console.log(chalk.blue(`🔍 Added rank role ${key}: ${id}`));
                        }
                    }
                });
                break;
            case 'world':
                const worldKeys = ['1', '2'];
                worldKeys.forEach(key => {
                    const roleId = config.dungeonAlert?.worldRoles?.[key];
                    if (roleId && roleId !== null && roleId !== 'null') {
                        const id = typeof roleId === 'string' ? roleId : String(roleId);
                        if (id && id !== 'null' && id !== 'undefined') {
                            roleIds.push(id);
                            console.log(chalk.blue(`🔍 Added world role ${key}: ${id}`));
                        }
                    }
                });
                break;
            case 'island':
                const islandRoles = config.dungeonAlert?.islandRoles || {};
                console.log(chalk.blue(`🔍 Debug island roles object:`, islandRoles));

                // Convert to plain object to avoid MongoDB properties
                const plainIslandRoles = JSON.parse(JSON.stringify(islandRoles));
                console.log(chalk.blue(`🔍 Debug plain island roles:`, plainIslandRoles));

                // Define the expected island names to avoid MongoDB properties
                const expectedIslands = [
                    'Leveling City', 'Grass Village', 'Brum Island', 'Faceheal Town',
                    'Lucky Kingdom', 'Nipon City', 'Mori Town', 'Dragon City',
                    'XZ City', 'Kindama City', 'Hunters City', 'Nen City', 'Hurricane Town', 'Cursed High'
                ];

                expectedIslands.forEach(islandName => {
                    const roleId = plainIslandRoles[islandName];
                    console.log(chalk.blue(`🔍 Processing island ${islandName}: ${roleId} (type: ${typeof roleId})`));
                    if (roleId && roleId !== null && roleId !== 'null') {
                        const id = typeof roleId === 'string' ? roleId : String(roleId);
                        if (id && id !== 'null' && id !== 'undefined' && /^\d+$/.test(id)) {
                            roleIds.push(id);
                            console.log(chalk.blue(`🔍 Added island role ${islandName}: ${id}`));
                        }
                    }
                });
                break;
            case 'special':
                const specialKeys = ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
                specialKeys.forEach(key => {
                    const roleId = config.dungeonAlert?.dungeonRoles?.[key];
                    if (roleId && roleId !== null && roleId !== 'null') {
                        const id = typeof roleId === 'string' ? roleId : String(roleId);
                        if (id && id !== 'null' && id !== 'undefined') {
                            roleIds.push(id);
                            console.log(chalk.blue(`🔍 Added special role ${key}: ${id}`));
                        }
                    }
                });
                break;
        }

        console.log(chalk.blue(`🔍 Final roleIds array:`, roleIds));
        return roleIds;
    },

    removeRolesFromConfig(config, roleType) {
        if (!config.dungeonAlert) return;

        switch (roleType) {
            case 'rank':
                const rankKeys = ['E', 'D', 'C', 'B', 'A', 'S', 'SS', 'G', 'N'];
                rankKeys.forEach(key => {
                    if (config.dungeonAlert.dungeonRoles) {
                        delete config.dungeonAlert.dungeonRoles[key];
                    }
                });
                break;
            case 'world':
                config.dungeonAlert.worldRoles = {};
                break;
            case 'island':
                config.dungeonAlert.islandRoles = {};
                break;
            case 'special':
                const specialKeys = ['DUNGEON_PING', 'RED_DUNGEON', 'DOUBLE_DUNGEON'];
                specialKeys.forEach(key => {
                    if (config.dungeonAlert.dungeonRoles) {
                        delete config.dungeonAlert.dungeonRoles[key];
                    }
                });
                break;
        }
    },

    // World Boss Functions
    async toggleAutoWorldBoss(interaction) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);

            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    dungeonAlert: { enabled: false },
                    worldBossAlert: {
                        enabled: false,
                        worldBossRoles: {}
                    },
                    infernalAlert: { enabled: false }
                };
            }

            if (!config.worldBossAlert) {
                config.worldBossAlert = {
                    enabled: false,
                    worldBossRoles: {}
                };
            }

            // Toggle the enabled state
            const newState = !config.worldBossAlert.enabled;
            config.worldBossAlert.enabled = newState;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Show updated setup
            await this.showAutoWorldBossSetup(interaction);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error toggling auto world boss:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to toggle auto world boss. Please try again.`,
                ephemeral: true
            });
        }
    },

    async showWorldBossRoleSelection(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.WORLD_BOSS} World Boss Ping Roles Setup`)
            .setDescription('Choose how you want to set up ping roles for world boss alerts:')
            .addFields([
                {
                    name: `${SETUP_EMOJIS.ROLES} General Role`,
                    value: 'General ping role for all world boss alerts',
                    inline: true
                },
                {
                    name: `${SETUP_EMOJIS.WORLD_BOSS} Boss Roles`,
                    value: 'Specific ping roles for individual bosses',
                    inline: true
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('worldboss_general_ping')
                    .setLabel(`${SETUP_EMOJIS.ROLES} General Role`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('worldboss_bosses_pings')
                    .setLabel(`${SETUP_EMOJIS.WORLD_BOSS} Boss Roles`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('worldboss_create_all_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_ALL} Create All Roles`)
                    .setStyle(ButtonStyle.Success)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, { step: 'worldboss_role_selection', feature: 'auto_worldboss' });
    },

    async showWorldBossGeneralPingOptions(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        let worldBossConfig = config?.worldBossAlert;

        // Migrate old world boss role structure if needed
        if (worldBossConfig) {
            worldBossConfig = await this.migrateWorldBossConfig(interaction.guild.id, worldBossConfig);
        }

        const currentRoles = worldBossConfig?.worldBossRoles || {};
        const isEnabled = worldBossConfig?.generalPingEnabled !== false; // Default to true

        const generalRoleId = currentRoles.general;
        const isConfigured = generalRoleId && interaction.guild.roles.cache.has(generalRoleId);

        const roleStatus = `**General Ping**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${generalRoleId}>` : `${SETUP_EMOJIS.ERROR} Not Set`}`;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.ROLES} General Ping Setup`)
            .setDescription('Set up general ping role for world boss alerts:')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${isConfigured ? '1/1' : '0/1'} role configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: roleStatus,
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create general world boss ping role\n• **Use Existing**: Assign existing server role',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('worldboss_create_general_role')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('worldboss_assign_existing_general')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('worldboss_toggle_general_ping')
                    .setLabel(isEnabled ? `Disable General Ping` : `Enable General Ping`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, { step: 'worldboss_general_ping_options', feature: 'auto_worldboss' });
    },

    async showWorldBossBossesPingOptions(interaction) {
        // Get current configuration to show what's already set
        const config = await configService.getServerConfig(interaction.guild.id);
        let worldBossConfig = config?.worldBossAlert;

        // Migrate old world boss role structure if needed
        if (worldBossConfig) {
            worldBossConfig = await this.migrateWorldBossConfig(interaction.guild.id, worldBossConfig);
        }

        const currentRoles = worldBossConfig?.worldBossRoles || {};
        const isEnabled = worldBossConfig?.bossesPingEnabled !== false; // Default to true

        console.log(chalk.blue(`🔍 Debug showWorldBossBossesPingOptions:`));
        console.log(chalk.blue(`📋 worldBossConfig:`, JSON.stringify(worldBossConfig, null, 2)));
        console.log(chalk.blue(`📋 currentRoles:`, JSON.stringify(currentRoles, null, 2)));

        const bossKeys = ['baruto', 'baizen', 'force', 'begeta', 'alien', 'don', 'protagonist', 'beach_ziru', 'beach_vermillion'];
        const bossNames = ['Baruto', 'Baizen', 'Force', 'Begeta', 'Alien', 'Don', 'Protagonist', 'Beach Ziru', 'Beach Vermillion'];

        const roleStatus = bossKeys.map((key, index) => {
            const roleId = currentRoles[key];
            const isConfigured = roleId && interaction.guild.roles.cache.has(roleId);
            return `**${bossNames[index]}**: ${isConfigured ? `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>` : `${SETUP_EMOJIS.ERROR} Not Set`}`;
        }).join('\n');

        const configuredCount = bossKeys.filter(key => {
            const roleId = currentRoles[key];
            return roleId && interaction.guild.roles.cache.has(roleId);
        }).length;

        const embed = new EmbedBuilder()
            .setColor(isEnabled ? '#0099ff' : '#ff9900')
            .setTitle(`${SETUP_EMOJIS.WORLD_BOSS} Bosses Pings Setup`)
            .setDescription('Set up individual boss ping roles for world boss alerts:')
            .addFields([
                {
                    name: 'Status',
                    value: `${isEnabled ? `${SETUP_EMOJIS.ENABLED} **Enabled**` : `${SETUP_EMOJIS.DISABLED} **Disabled**`} | ${configuredCount}/${bossKeys.length} roles configured`,
                    inline: false
                },
                {
                    name: 'Current Configuration',
                    value: roleStatus,
                    inline: false
                },
                {
                    name: 'Options',
                    value: '• **Create New**: Auto-create all boss ping roles with proper colors\n• **Use Existing**: Assign existing server roles individually',
                    inline: false
                }
            ])
            .setTimestamp();

        const row1 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('worldboss_create_boss_roles')
                    .setLabel(`${SETUP_EMOJIS.CREATE_NEW} Create New`)
                    .setStyle(ButtonStyle.Success),
                new ButtonBuilder()
                    .setCustomId('worldboss_assign_existing_boss_roles')
                    .setLabel(`${SETUP_EMOJIS.USE_EXISTING} Use Existing`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        const row2 = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('worldboss_toggle_bosses_ping')
                    .setLabel(isEnabled ? `Disable Bosses Ping` : `Enable Bosses Ping`)
                    .setEmoji(isEnabled ? SETUP_EMOJIS.DISABLE : SETUP_EMOJIS.ENABLE)
                    .setStyle(isEnabled ? ButtonStyle.Danger : ButtonStyle.Success)
            );

        await interaction.update({ embeds: [embed], components: [row1, row2] });

        this.updateSession(interaction.user.id, { step: 'worldboss_bosses_ping_options', feature: 'auto_worldboss' });
    },

    async createWorldBossGeneralRole(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don't have permission to create roles. Please give me the "Manage Roles" permission.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration from database
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        worldBossRoles: {}
                    }
                };
            }
            if (!config.worldBossAlert) {
                config.worldBossAlert = {
                    enabled: false,
                    worldBossRoles: {}
                };
            }

            // Check if general role already exists and is valid
            const generalRoleId = config.worldBossAlert.worldBossRoles?.general;
            const roleExists = generalRoleId && interaction.guild.roles.cache.has(generalRoleId);

            if (roleExists) {
                // Show "Already configured" message like auto dungeons
                const embed = new EmbedBuilder()
                    .setColor('#00ff00')
                    .setTitle(`${SETUP_EMOJIS.SUCCESS} General Ping Role Already Configured`)
                    .setDescription(`The general ping role is already configured and exists in this server.`)
                    .addFields([
                        {
                            name: 'Configured Role',
                            value: `<@&${generalRoleId}>`,
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('worldboss_general_ping')
                            .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Back to General Setup`)
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('setup_back')
                            .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.LOADING} Creating general world boss ping role... This may take a moment.`,
                embeds: [],
                components: []
            });

            // Create general world boss ping role
            const generalRole = await interaction.guild.roles.create({
                name: 'World Boss Ping',
                color: '#ff6b35', // Orange color for world boss
                reason: 'Created by setup command for world boss alerts'
            });

            // Save to database
            //let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        worldBossRoles: {
                            general: generalRole.id
                        }
                    }
                };
            } else {
                if (!config.worldBossAlert) {
                    config.worldBossAlert = {
                        enabled: false,
                        worldBossRoles: {
                            general: generalRole.id
                        }
                    };
                } else {
                    if (!config.worldBossAlert.worldBossRoles) {
                        config.worldBossAlert.worldBossRoles = {};
                    }
                    config.worldBossAlert.worldBossRoles.general = generalRole.id;
                }
            }
            await configService.saveServerConfig(interaction.guild.id, config);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} General World Boss Role Created`)
                .setDescription('Successfully created general world boss ping role!')
                .addFields([
                    {
                        name: 'Created Role',
                        value: `${SETUP_EMOJIS.WORLD_BOSS} General: <@&${generalRole.id}>`,
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to World Boss Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            // Update session to ensure back navigation works correctly
            this.updateSession(interaction.user.id, {
                step: 'worldboss_general_role_created',
                feature: 'auto_worldboss'
            });

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating general world boss role:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create general world boss role. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async createWorldBossBossRoles(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don't have permission to create roles. Please give me the "Manage Roles" permission.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration from database
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        worldBossRoles: {}
                    }
                };
            }
            if (!config.worldBossAlert) {
                config.worldBossAlert = {
                    enabled: false,
                    worldBossRoles: {}
                };
            }

            // Convert MongoDB objects to plain objects to avoid MongoDB properties
            const plainWorldBossRoles = JSON.parse(JSON.stringify(config.worldBossAlert.worldBossRoles || {}));

            // Define all boss roles that would be created
            const bossKeys = ['baruto', 'baizen', 'force', 'begeta', 'alien', 'don', 'protagonist', 'beach_ziru', 'beach_vermillion'];
            const bossNames = ['Baruto', 'Baizen', 'Force', 'Begeta', 'Alien', 'Don', 'Protagonist', 'Beach Ziru', 'Beach Vermillion'];

            const rolesToCreate = bossKeys.map((key, index) => ({
                name: `${bossNames[index]} Ping`,
                key: key,
                color: '#ff6b35'
            }));

            // Check which roles already exist in the configuration
            const existingRoles = [];
            const missingRoles = [];

            console.log(chalk.blue(`🔍 Checking ${rolesToCreate.length} boss roles...`));

            for (const roleInfo of rolesToCreate) {
                const roleId = plainWorldBossRoles[roleInfo.key];

                // Ensure roleId is a string and validate format
                const roleIdString = typeof roleId === 'string' ? roleId : String(roleId);
                const isValidId = roleId && /^\d{17,19}$/.test(roleIdString);

                // Check if role exists in Discord
                const roleExists = isValidId && interaction.guild.roles.cache.has(roleIdString);

                if (roleExists) {
                    existingRoles.push({ ...roleInfo, roleId: roleIdString });
                    console.log(chalk.green(`✅ ${roleInfo.name} exists: ${roleIdString}`));
                } else {
                    missingRoles.push(roleInfo);
                    console.log(chalk.yellow(`❌ ${roleInfo.name} missing (roleId: ${roleId || 'none'}, valid: ${isValidId}, exists: ${roleExists})`));
                }
            }

            console.log(chalk.blue(`📊 Results: ${existingRoles.length} existing, ${missingRoles.length} missing`));

            // If all roles exist, show "All Roles Already Configured" message
            if (existingRoles.length === rolesToCreate.length) {
                const roleList = existingRoles
                    .map(role => {
                        const roleId = typeof role.roleId === 'string' ? role.roleId : String(role.roleId);
                        if (/^\d{17,19}$/.test(roleId)) {
                            return `<@&${roleId}>`;
                        } else {
                            return `**${role.name}** (Invalid ID)`;
                        }
                    })
                    .join(', ');

                const embed = new EmbedBuilder()
                    .setColor('#00ff00')
                    .setTitle(`${SETUP_EMOJIS.SUCCESS} All Boss Ping Roles Already Configured`)
                    .setDescription(`All boss ping roles are already configured and exist in this server.`)
                    .addFields([
                        {
                            name: 'Configured Roles',
                            value: roleList || 'None found',
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('worldboss_bosses_pings')
                            .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Back to Boss Setup`)
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('setup_back')
                            .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            // If some roles exist but some are missing, show confirmation dialog
            if (existingRoles.length > 0) {
                const existingRoleList = existingRoles
                    .map(role => {
                        const roleId = typeof role.roleId === 'string' ? role.roleId : String(role.roleId);
                        if (/^\d{17,19}$/.test(roleId)) {
                            return `<@&${roleId}>`;
                        } else {
                            return `**${role.name}** (Invalid ID)`;
                        }
                    })
                    .join(', ');

                const missingRoleList = missingRoles
                    .map(role => `**${role.name}**`)
                    .join(', ');

                const embed = new EmbedBuilder()
                    .setColor('#ff9900')
                    .setTitle(`${SETUP_EMOJIS.WARNING} Some Boss Ping Roles Already Configured`)
                    .setDescription(`Some boss ping roles are already configured, but some are missing. Would you like me to create the missing ones?`)
                    .addFields([
                        {
                            name: `${SETUP_EMOJIS.SUCCESS} Already Configured (${existingRoles.length})`,
                            value: existingRoleList || 'None found',
                            inline: false
                        },
                        {
                            name: `${SETUP_EMOJIS.ERROR} Missing Roles (${missingRoles.length})`,
                            value: missingRoleList || 'None',
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('create_missing_worldboss_roles')
                            .setLabel(`${SETUP_EMOJIS.SUCCESS} Yes, Create Missing Roles`)
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('worldboss_bosses_pings')
                            .setLabel(`${SETUP_EMOJIS.CANCEL} No, Go Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                // Store missing roles in session for later use
                this.updateSession(interaction.user.id, {
                    missingWorldBossRoles: missingRoles,
                    roleCategory: 'Boss Ping Roles'
                });

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            // If no roles exist, proceed with normal creation
            await this.proceedWithWorldBossRoleCreation(interaction, rolesToCreate, config);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating boss ping roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create boss ping roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async proceedWithWorldBossRoleCreation(interaction, rolesToCreate, config) {
        try {
            await interaction.update({
                content: `${SETUP_EMOJIS.LOADING} Creating boss ping roles... This may take a moment.`,
                embeds: [],
                components: []
            });

            const createdRoles = {};
            const totalRoles = rolesToCreate.length;
            let roleCount = 0;

            // Create progress embed
            const progressEmbed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.PROGRESS} Creating Boss Ping Roles`)
                .setDescription('Please wait while I create all the boss ping roles...')
                .addFields([
                    { name: '⚙️ Status', value: `🔄 Creating roles... (0/${totalRoles})`, inline: true }
                ])
                .setTimestamp();

            await interaction.editReply({ embeds: [progressEmbed], components: [] });

            // Create each boss role
            for (const roleInfo of rolesToCreate) {
                try {
                    roleCount++;

                    // Update progress
                    progressEmbed.setFields(
                        { name: '⚙️ Status', value: `🔄 Creating roles... (${roleCount}/${totalRoles})\nCurrent: **${roleInfo.name}**`, inline: true }
                    );
                    await interaction.editReply({ embeds: [progressEmbed] });

                    const role = await interaction.guild.roles.create({
                        name: roleInfo.name,
                        color: roleInfo.color,
                        reason: 'Created by setup command for world boss alerts'
                    });

                    createdRoles[roleInfo.key] = role.id;
                    console.log(chalk.green(`✅ Created role: ${roleInfo.name} -> Key: ${roleInfo.key}, ID: ${role.id}`));

                    // Small delay to avoid rate limits
                    await new Promise(resolve => setTimeout(resolve, 150));
                } catch (error) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to create role ${roleInfo.name}:`), error);
                }
            }

            // Update progress to saving configuration
            progressEmbed.setFields(
                { name: '⚙️ Status', value: '💾 Saving configuration...', inline: true }
            );
            await interaction.editReply({ embeds: [progressEmbed] });

            // Save to database
            if (!config.worldBossAlert.worldBossRoles) {
                config.worldBossAlert.worldBossRoles = {};
            }
            Object.assign(config.worldBossAlert.worldBossRoles, createdRoles);

            console.log(chalk.blue(`💾 Saving world boss roles to database:`));
            console.log(chalk.blue(`📋 Created roles:`, JSON.stringify(createdRoles, null, 2)));
            console.log(chalk.blue(`📋 Final config worldBossRoles:`, JSON.stringify(config.worldBossAlert.worldBossRoles, null, 2)));

            await configService.saveServerConfig(interaction.guild.id, config);
            console.log(chalk.green(`✅ Successfully saved world boss config to database`));

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} Boss Ping Roles Created`)
                .setDescription('Successfully created boss ping roles!')
                .addFields([
                    {
                        name: 'Created Roles',
                        value: Object.values(createdRoles).map(roleId => `<@&${roleId}>`).join(', '),
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('worldboss_bosses_pings')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to Boss Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            // Update session to ensure back navigation works correctly
            this.updateSession(interaction.user.id, {
                step: 'worldboss_boss_roles_created',
                feature: 'auto_worldboss'
            });

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating boss ping roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create boss ping roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async createAllWorldBossRoles(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don't have permission to create roles. Please give me the "Manage Roles" permission.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration from database
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        worldBossRoles: {}
                    }
                };
            }
            if (!config.worldBossAlert) {
                config.worldBossAlert = {
                    enabled: false,
                    worldBossRoles: {}
                };
            }

            // Convert MongoDB objects to plain objects
            const plainWorldBossRoles = JSON.parse(JSON.stringify(config.worldBossAlert.worldBossRoles || {}));

            // Define all roles that would be created (including general)
            const allRoles = [
                { name: 'World Boss Ping', key: 'general', color: '#ff6b35' },
                { name: 'Baruto Ping', key: 'baruto', color: '#ff6b35' },
                { name: 'Baizen Ping', key: 'baizen', color: '#ff6b35' },
                { name: 'Force Ping', key: 'force', color: '#ff6b35' },
                { name: 'Begeta Ping', key: 'begeta', color: '#ff6b35' },
                { name: 'Alien Ping', key: 'alien', color: '#ff6b35' },
                { name: 'Don Ping', key: 'don', color: '#ff6b35' },
                { name: 'Protagonist Ping', key: 'protagonist', color: '#ff6b35' },
                { name: 'Beach Ziru Ping', key: 'beach_ziru', color: '#ff6b35' },
                { name: 'Beach Vermillion Ping', key: 'beach_vermillion', color: '#ff6b35' }
            ];

            // Check which roles already exist
            const existingRoles = [];
            const missingRoles = [];

            for (const roleInfo of allRoles) {
                const roleId = plainWorldBossRoles[roleInfo.key];
                const roleIdString = typeof roleId === 'string' ? roleId : String(roleId);
                const isValidId = roleId && /^\d{17,19}$/.test(roleIdString);
                const roleExists = isValidId && interaction.guild.roles.cache.has(roleIdString);

                if (roleExists) {
                    existingRoles.push({ ...roleInfo, roleId: roleIdString });
                } else {
                    missingRoles.push(roleInfo);
                }
            }

            // If all roles exist, show message
            if (existingRoles.length === allRoles.length) {
                const roleList = existingRoles
                    .map(role => `<@&${role.roleId}>`)
                    .join(', ');

                const embed = new EmbedBuilder()
                    .setColor('#00ff00')
                    .setTitle(`${SETUP_EMOJIS.SUCCESS} All World Boss Roles Already Configured`)
                    .setDescription(`All world boss roles (general + boss-specific) are already configured and exist in this server.`)
                    .addFields([
                        {
                            name: 'Configured Roles',
                            value: roleList || 'None found',
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('worldboss_bosses_pings')
                            .setLabel(`${SETUP_EMOJIS.MORE_SETUP} Back to Boss Setup`)
                            .setStyle(ButtonStyle.Primary),
                        new ButtonBuilder()
                            .setCustomId('setup_back')
                            .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            // If some roles exist, show confirmation dialog
            if (existingRoles.length > 0) {
                const existingRoleList = existingRoles
                    .map(role => `<@&${role.roleId}>`)
                    .join(', ');

                const missingRoleList = missingRoles
                    .map(role => `**${role.name}**`)
                    .join(', ');

                const embed = new EmbedBuilder()
                    .setColor('#ff9900')
                    .setTitle(`${SETUP_EMOJIS.WARNING} Some World Boss Roles Already Configured`)
                    .setDescription(`Some world boss roles are already configured, but some are missing. Would you like me to create the missing ones?`)
                    .addFields([
                        {
                            name: `${SETUP_EMOJIS.SUCCESS} Already Configured (${existingRoles.length})`,
                            value: existingRoleList.length > 1000 ? existingRoleList.substring(0, 1000) + '...' : existingRoleList,
                            inline: false
                        },
                        {
                            name: `${SETUP_EMOJIS.ERROR} Missing Roles (${missingRoles.length})`,
                            value: missingRoleList.length > 1000 ? missingRoleList.substring(0, 1000) + '...' : missingRoleList,
                            inline: false
                        }
                    ])
                    .setTimestamp();

                const row = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('create_missing_worldboss_all_roles')
                            .setLabel(`${SETUP_EMOJIS.SUCCESS} Yes, Create Missing Roles`)
                            .setStyle(ButtonStyle.Success),
                        new ButtonBuilder()
                            .setCustomId('worldboss_bosses_pings')
                            .setLabel(`${SETUP_EMOJIS.CANCEL} No, Go Back`)
                            .setStyle(ButtonStyle.Secondary)
                    );

                // Store missing roles in session
                this.updateSession(interaction.user.id, {
                    missingWorldBossAllRoles: missingRoles,
                    roleCategory: 'All World Boss Roles'
                });

                return await interaction.update({ embeds: [embed], components: [row] });
            }

            // If no roles exist, proceed with creation
            await this.proceedWithWorldBossRoleCreation(interaction, allRoles, config);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating all world boss roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create all world boss roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async createMissingWorldBossRoles(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const missingRoles = session?.missingWorldBossRoles;
            const roleCategory = session?.roleCategory;

            if (!missingRoles || missingRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No missing roles found in session. Please try again.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config?.worldBossAlert) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        worldBossRoles: {}
                    }
                };
            }

            // Proceed with creating only the missing roles
            await this.proceedWithWorldBossRoleCreation(interaction, missingRoles, config);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating missing world boss roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create missing world boss roles. Please try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async createMissingWorldBossAllRoles(interaction) {
        try {
            const session = this.getSession(interaction.user.id);
            const missingRoles = session?.missingWorldBossAllRoles;

            if (!missingRoles || missingRoles.length === 0) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No missing roles found in session. Please try again.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config?.worldBossAlert) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        worldBossRoles: {}
                    }
                };
            }

            // Proceed with creating only the missing roles
            await this.proceedWithWorldBossRoleCreation(interaction, missingRoles, config);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating missing world boss all roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create missing world boss roles. Please try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async showExistingGeneralRoles(interaction) {
        try {
            // Get current configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            const currentGeneralRole = config?.worldBossAlert?.worldBossRoles?.general;

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.ASSIGN} Assign Existing General World Boss Role`)
                .setDescription('Assign an existing role from your server as the general world boss ping role.')
                .addFields([
                    {
                        name: 'Current Status',
                        value: currentGeneralRole
                            ? (interaction.guild.roles.cache.has(currentGeneralRole)
                                ? `${SETUP_EMOJIS.SUCCESS} <@&${currentGeneralRole}>`
                                : `${SETUP_EMOJIS.INVALID_ROLE} Invalid Role (ID: ${currentGeneralRole})`)
                            : `${SETUP_EMOJIS.ERROR} Not configured`,
                        inline: false
                    },
                    {
                        name: 'Instructions',
                        value: 'Please provide the role information in one of these formats:\n• **Role mention**: @RoleName\n• **Role ID**: 123456789012345678\n\n*You have 60 seconds to respond.*',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({ embeds: [embed], components: [row] });

            // Update session to track what we're assigning
            this.updateSession(interaction.user.id, {
                step: 'worldboss_general_role_input',
                roleType: 'worldboss_general',
                roleKey: 'general',
                displayName: 'General World Boss Role',
                channelId: interaction.channel.id,
                userId: interaction.user.id
            });

            // Create a collector to wait for the user's role input
            const filter = (message) => message.author.id === interaction.user.id;
            const collector = interaction.channel.createMessageCollector({
                filter,
                time: 60000, // 1 minute timeout
                max: 1
            });

            await interaction.followUp({
                content: `${SETUP_EMOJIS.ROLE_INPUT} **Assigning General World Boss Role**\n\nPlease provide the role information in one of these formats:\n• **Role mention**: @RoleName\n• **Role ID**: 123456789012345678\n\n*You have 60 seconds to respond.*`,
                ephemeral: true
            });

            collector.on('collect', async (message) => {
                try {
                    await this.processWorldBossRoleInput(message, 'worldboss_general', 'general');
                } catch (error) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing world boss role input:`), error);
                    await message.reply(`${SETUP_EMOJIS.ERROR} An error occurred while processing your role input. Please try again.`);
                }
            });

            collector.on('end', (collected) => {
                if (collected.size === 0) {
                    interaction.followUp({
                        content: `${SETUP_EMOJIS.TIMEOUT_WARNING} Role assignment timed out. Please try again.`,
                        ephemeral: true
                    }).catch(console.error);
                }
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing existing general roles:`), error);
            await interaction.update({
                content: `${SETUP_EMOJIS.ERROR} An error occurred while loading roles. Please try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async showExistingBossRoles(interaction) {
        try {
            // Get current configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            const currentBossRoles = config?.worldBossAlert?.worldBossRoles || {};

            // Define all possible world boss names (9 bosses total)
            const worldBossNames = [
                'baruto', 'baizen', 'force', 'begeta', 'alien',
                'don', 'protagonist', 'beach_ziru', 'beach_vermillion'
            ];

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.ASSIGN} Assign Existing Boss Roles`)
                .setDescription('Select which boss role you want to assign:')
                .addFields([
                    {
                        name: 'Instructions',
                        value: '1. Choose a boss from the dropdown below\n2. After selecting, provide the role ID or mention the role in chat\n3. The role will be validated and assigned',
                        inline: false
                    }
                ])
                .setTimestamp();

            // Show current boss role configuration
            const configuredBosses = Object.entries(currentBossRoles)
                .filter(([key]) => key !== 'general' && worldBossNames.includes(key))
                .map(([bossKey, roleId]) => {
                    const isValid = interaction.guild.roles.cache.has(roleId);
                    const bossName = bossKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
                    return `**${bossName}**: ${isValid ? `${SETUP_EMOJIS.VALIDATE} <@&${roleId}>` : `${SETUP_EMOJIS.INVALID_ROLE} Invalid Role`}`;
                });

            if (configuredBosses.length > 0) {
                embed.addFields([
                    {
                        name: 'Current Boss Role Configuration',
                        value: configuredBosses.join('\n'),
                        inline: false
                    }
                ]);
            }

            // Create dropdown with boss options
            const options = worldBossNames.map(bossKey => {
                const isConfigured = currentBossRoles[bossKey] && interaction.guild.roles.cache.has(currentBossRoles[bossKey]);
                const bossName = bossKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

                return {
                    label: bossName,
                    value: `assign_boss_${bossKey}`,
                    description: isConfigured ? 'Currently configured - select to update' : 'Not configured',
                    emoji: isConfigured ? SETUP_EMOJIS.SUCCESS : SETUP_EMOJIS.ERROR
                };
            });

            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('select_boss_role_to_assign')
                .setPlaceholder('Choose a boss to assign a role...')
                .addOptions(options);

            const row1 = new ActionRowBuilder().addComponents(selectMenu);

            const row2 = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({
                embeds: [embed],
                components: [row1, row2]
            });

            this.updateSession(interaction.user.id, {
                step: 'worldboss_boss_role_assignment',
                currentRoleType: 'worldboss_boss'
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error showing existing boss roles:`), error);
            await interaction.update({
                content: `${SETUP_EMOJIS.ERROR} An error occurred while loading boss roles. Please try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async handleGeneralRoleAssignment(interaction) {
        try {
            const selectedValue = interaction.values[0];
            const roleId = selectedValue.replace('assign_general_', '');

            // Validate the role exists
            const role = interaction.guild.roles.cache.get(roleId);
            if (!role) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} Selected role not found. Please try again.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration
            const config = await configService.getServerConfig(interaction.guild.id) || {
                serverId: interaction.guild.id,
                name: interaction.guild.name,
                dungeonAlert: { enabled: false },
                worldBossAlert: { enabled: false },
                infernalAlert: { enabled: false }
            };

            // Initialize worldBossAlert if it doesn't exist
            if (!config.worldBossAlert) {
                config.worldBossAlert = { enabled: false };
            }

            // Initialize worldBossRoles if it doesn't exist
            if (!config.worldBossAlert.worldBossRoles) {
                config.worldBossAlert.worldBossRoles = {};
            }

            // Assign the general role
            config.worldBossAlert.worldBossRoles.general = roleId;

            // Save configuration
            await configService.saveServerConfig(interaction.guild.id, config);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} General Role Assigned`)
                .setDescription(`Successfully assigned **${role.name}** as the general world boss ping role.`)
                .addFields([
                    {
                        name: 'Assigned Role',
                        value: `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>`,
                        inline: false
                    },
                    {
                        name: 'What This Means',
                        value: 'This role will be pinged for all world boss alerts, regardless of which specific boss spawns.',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to World Boss Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error assigning general role:`), error);
            await interaction.update({
                content: `${SETUP_EMOJIS.ERROR} An error occurred while assigning the role. Please try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async handleBossRoleSelectionForInput(interaction) {
        try {
            console.log(chalk.cyan(`🔧 handleBossRoleSelectionForInput called with values:`, interaction.values));
            
            const selectedValue = interaction.values[0];
            const bossKey = selectedValue.replace('assign_boss_', '');
            const bossName = bossKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

            console.log(chalk.blue(`📋 Processing boss selection: ${bossKey} (${bossName})`));

            // Get current configuration to show current status
            const config = await configService.getServerConfig(interaction.guild.id);
            const currentBossRoles = config?.worldBossAlert?.worldBossRoles || {};
            const currentRoleId = currentBossRoles[bossKey];

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`${SETUP_EMOJIS.ASSIGN} Assign Role for ${bossName}`)
                .setDescription(`Assign an existing role to ping when **${bossName}** spawns as a world boss.`)
                .addFields([
                    {
                        name: 'Current Status',
                        value: currentRoleId
                            ? (interaction.guild.roles.cache.has(currentRoleId)
                                ? `${SETUP_EMOJIS.SUCCESS} <@&${currentRoleId}>`
                                : `${SETUP_EMOJIS.INVALID_ROLE} Invalid Role (ID: ${currentRoleId})`)
                            : `${SETUP_EMOJIS.ERROR} Not configured`,
                        inline: false
                    },
                    {
                        name: 'Instructions',
                        value: 'Please provide the role information in one of these formats:\n• **Role mention**: @RoleName\n• **Role ID**: 123456789012345678\n\n*You have 60 seconds to respond.*',
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                        .setStyle(ButtonStyle.Secondary)
                );

            console.log(chalk.green(`✅ About to update interaction with embed`));
            await interaction.update({ embeds: [embed], components: [row] });
            console.log(chalk.green(`✅ Interaction updated successfully`));

            // Update session to track what we're assigning
            this.updateSession(interaction.user.id, {
                step: 'worldboss_boss_role_input',
                roleType: 'worldboss_boss',
                roleKey: bossKey,
                displayName: `${bossName} Boss Role`,
                channelId: interaction.channel.id,
                userId: interaction.user.id
            });
            console.log(chalk.green(`✅ Session updated for user ${interaction.user.id}`));

            // Create a collector to wait for the user's role input
            const filter = (message) => message.author.id === interaction.user.id;
            const collector = interaction.channel.createMessageCollector({
                filter,
                time: 60000, // 1 minute timeout
                max: 1
            });
            console.log(chalk.green(`✅ Message collector created`));

            try {
                console.log(chalk.cyan(`🔧 Attempting to send followUp message`));
                await interaction.followUp({
                    content: `${SETUP_EMOJIS.ROLE_INPUT} **Assigning ${bossName} Boss Role**\n\nPlease provide the role information in one of these formats:\n• **Role mention**: @RoleName\n• **Role ID**: 123456789012345678\n\n*You have 60 seconds to respond.*`,
                    ephemeral: true
                });
                console.log(chalk.green(`✅ FollowUp message sent successfully`));
            } catch (followUpError) {
                console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error sending followUp message:`), followUpError);
                console.log(chalk.yellow(`⚠️ Trying to send to channel instead`));
                try {
                    await interaction.channel.send(`${SETUP_EMOJIS.ROLE_INPUT} **Assigning ${bossName} Boss Role**\n\n<@${interaction.user.id}> Please provide the role information in one of these formats:\n• **Role mention**: @RoleName\n• **Role ID**: 123456789012345678\n\n*You have 60 seconds to respond.*`);
                    console.log(chalk.green(`✅ Channel message sent successfully`));
                } catch (channelError) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error sending channel message:`), channelError);
                }
            }

            collector.on('collect', async (message) => {
                try {
                    console.log(chalk.cyan(`📨 Collector received message: ${message.content}`));
                    await this.processWorldBossRoleInput(message, 'worldboss_boss', bossKey);
                } catch (error) {
                    console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error processing world boss role input:`), error);
                    await message.channel.send(`${SETUP_EMOJIS.ERROR} An error occurred while processing your role input. Please try again.`);
                }
            });

            collector.on('end', (collected) => {
                console.log(chalk.blue(`📋 Collector ended. Collected ${collected.size} messages`));
                if (collected.size === 0) {
                    interaction.followUp({
                        content: `${SETUP_EMOJIS.TIMEOUT_WARNING} Role assignment timed out. Please try again.`,
                        ephemeral: true
                    }).catch(console.error);
                }
            });

            console.log(chalk.green(`✅ handleBossRoleSelectionForInput completed successfully`));

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error in handleBossRoleSelectionForInput:`), error);
            try {
                await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} An error occurred while loading the role assignment interface. Please try again.`,
                    embeds: [],
                    components: []
                });
            } catch (updateError) {
                console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error updating interaction after error:`), updateError);
            }
        }
    },

    async handleSpecificBossRoleAssignment(interaction) {
        try {
            const selectedValue = interaction.values[0];
            const parts = selectedValue.split('_');
            const roleId = parts[parts.length - 1];
            const bossKey = parts.slice(3, -1).join('_');

            // Validate the role exists
            const role = interaction.guild.roles.cache.get(roleId);
            if (!role) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} Selected role not found. Please try again.`,
                    embeds: [],
                    components: []
                });
            }

            // Get current configuration
            const config = await configService.getServerConfig(interaction.guild.id) || {
                serverId: interaction.guild.id,
                name: interaction.guild.name,
                dungeonAlert: { enabled: false },
                worldBossAlert: { enabled: false },
                infernalAlert: { enabled: false }
            };

            // Initialize worldBossAlert if it doesn't exist
            if (!config.worldBossAlert) {
                config.worldBossAlert = { enabled: false };
            }

            // Initialize worldBossRoles if it doesn't exist
            if (!config.worldBossAlert.worldBossRoles) {
                config.worldBossAlert.worldBossRoles = {};
            }

            // Assign the boss-specific role
            config.worldBossAlert.worldBossRoles[bossKey] = roleId;

            // Save configuration
            await configService.saveServerConfig(interaction.guild.id, config);

            const bossName = bossKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} Boss Role Assigned`)
                .setDescription(`Successfully assigned **${role.name}** for **${bossName}** world boss alerts.`)
                .addFields([
                    {
                        name: 'Boss',
                        value: bossName,
                        inline: true
                    },
                    {
                        name: 'Assigned Role',
                        value: `${SETUP_EMOJIS.SUCCESS} <@&${roleId}>`,
                        inline: true
                    },
                    {
                        name: 'What This Means',
                        value: `This role will be pinged specifically when **${bossName}** spawns as a world boss.`,
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to World Boss Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.update({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error assigning boss role:`), error);
            await interaction.update({
                content: `${SETUP_EMOJIS.ERROR} An error occurred while assigning the role. Please try again.`,
                embeds: [],
                components: []
            });
        }
    },

    async testSendWorldBossAlert(interaction) {
        try {
            // Check cooldown first
            const userId = interaction.user.id;
            const now = Date.now();
            const lastUsed = testWorldBossCooldowns.get(userId);

            if (lastUsed && (now - lastUsed) < TEST_WORLDBOSS_COOLDOWN) {
                const remainingTime = Math.ceil((TEST_WORLDBOSS_COOLDOWN - (now - lastUsed)) / 1000);
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.TIMEOUT} Please wait **${remainingTime} seconds** before testing world boss alert again.`,
                    ephemeral: true
                });
            }

            // Get current configuration
            const config = await configService.getServerConfig(interaction.guild.id);
            const worldBossConfig = config?.worldBossAlert;

            // Check session for selected channel (not yet saved to database)
            const session = this.getSession(interaction.user.id);
            const selectedChannelId = session?.selectedChannelId;

            // Determine target channel
            const targetChannelId = selectedChannelId || worldBossConfig?.targetChannelId;

            if (!targetChannelId) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} No channel is set up for world boss alerts. Please set up a channel first before testing.`,
                    ephemeral: true
                });
            }

            // Validate channel exists
            const targetChannel = interaction.guild.channels.cache.get(targetChannelId);
            if (!targetChannel) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} The configured channel <#${targetChannelId}> was not found. Please set up a valid channel first.`,
                    ephemeral: true
                });
            }

            // Check if bot has permission to send messages in the target channel
            if (!targetChannel.permissionsFor(interaction.guild.members.me).has(PermissionFlagsBits.SendMessages)) {
                return await interaction.reply({
                    content: `${SETUP_EMOJIS.ERROR} I don't have permission to send messages in ${targetChannel}. Please check my permissions.`,
                    ephemeral: true
                });
            }

            await interaction.reply({
                content: `${SETUP_EMOJIS.LOADING} Sending test world boss alert to ${targetChannel}...`,
                ephemeral: true
            });

            // Create test world boss data (same format as real alerts)
            const testWorldBossData = {
                title: 'WORLD BOSS ALERT',
                worldBosses: {
                    1: {
                        boss: 'Baruto',
                        is_active: true,
                        world: 1
                    },
                    2: {
                        boss: 'Beach Vermillion',
                        is_active: true,
                        world: 2
                    }
                },
                worldBossPings: {},
                timestamp: new Date().toISOString(),
                lastUpdate: new Date().toISOString(),
                nextSpawnCountdown: null,
                type: 'world_boss',
                source: 'test'
            };
            // Use the WorldBossAlert class to create the image (same as real alerts)
            const WorldBossAlert = require('../../../modules/worldBossAlert');
            const worldBossAlert = new WorldBossAlert(interaction.client);
            const imageBuffer = await worldBossAlert.createWorldBossImage(testWorldBossData);

            if (!imageBuffer) {
                return await interaction.editReply({
                    content: `${SETUP_EMOJIS.ERROR} Failed to create world boss test image. Please try again.`
                });
            }

            const attachment = new AttachmentBuilder(imageBuffer, { name: 'worldboss_alert.png' });

            // Create test world boss embed using the same format as real alerts
            const testEmbed = new EmbedBuilder()
                .setTitle('🌍 WORLD BOSS ALERT')
                .setImage('attachment://worldboss_alert.png')
                .setColor('#aa44ff')
                .setFooter({
                    text: `This is a test world boss alert ran by ${interaction.user.displayName}`
                });

            // Send the test embed to the target channel (no pings in test)
            await targetChannel.send({
                embeds: [testEmbed],
                files: [attachment]
            });

            // Set cooldown for this user
            testWorldBossCooldowns.set(userId, now);

            // Auto-cleanup cooldown after expiry
            setTimeout(() => {
                testWorldBossCooldowns.delete(userId);
            }, TEST_WORLDBOSS_COOLDOWN);

            // Update the reply to show success
            await interaction.editReply({
                content: `${SETUP_EMOJIS.SUCCESS} Test world boss alert sent successfully to ${targetChannel}! Check the channel to see how world boss alerts will look.`
            });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error sending test world boss alert:`), error);

            const errorMessage = this.getDetailedErrorMessage(error);

            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({
                    content: errorMessage
                });
            } else {
                await interaction.reply({
                    content: errorMessage,
                    ephemeral: true
                });
            }
        }
    },

    async migrateWorldBossConfig(guildId, worldBossConfig) {
        try {
            // Since we no longer support old island-based structure, just ensure the config has the required fields
            const migratedConfig = {
                enabled: worldBossConfig.enabled || false,
                targetChannelId: worldBossConfig.targetChannelId,
                generalPingEnabled: worldBossConfig.generalPingEnabled !== false, // Default to true
                bossesPingEnabled: worldBossConfig.bossesPingEnabled !== false, // Default to true
                worldBossRoles: worldBossConfig.worldBossRoles || {}
            };

            // Remove old roleId field if it exists and move to general if needed
            if (worldBossConfig.roleId && !migratedConfig.worldBossRoles.general) {
                migratedConfig.worldBossRoles.general = worldBossConfig.roleId;
                console.log(chalk.yellow(`${SETUP_EMOJIS.WARNING} Moved legacy roleId to general role for guild ${guildId}`));
            }

            return migratedConfig;

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error migrating world boss config:`), error);
            // Return original config if migration fails
            return worldBossConfig;
        }
    },

    async toggleWorldBossGeneralPing(interaction) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);

            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        generalPingEnabled: false,
                        worldBossRoles: {}
                    }
                };
            }

            if (!config.worldBossAlert) {
                config.worldBossAlert = {
                    enabled: false,
                    generalPingEnabled: false,
                    worldBossRoles: {}
                };
            }

            // Toggle the enabled state
            const newState = !config.worldBossAlert.generalPingEnabled;
            config.worldBossAlert.generalPingEnabled = newState;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Show updated setup
            await this.showWorldBossGeneralPingOptions(interaction);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error toggling general ping:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to toggle general ping. Please try again.`,
                ephemeral: true
            });
        }
    },

    async toggleWorldBossBossesPing(interaction) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);

            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        bossesPingEnabled: false,
                        worldBossRoles: {}
                    }
                };
            }

            if (!config.worldBossAlert) {
                config.worldBossAlert = {
                    enabled: false,
                    bossesPingEnabled: false,
                    worldBossRoles: {}
                };
            }

            const currentState = config.worldBossAlert.bossesPingEnabled !== false;

            // If currently enabled and trying to disable, show options
            if (currentState) {
                await this.showWorldBossDisableConfirmation(interaction, 'bosses');
                return;
            }

            // If currently disabled, just enable it
            config.worldBossAlert.bossesPingEnabled = true;

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Show updated setup
            await this.showWorldBossBossesPingOptions(interaction);

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error toggling bosses ping:`), error);
            await interaction.reply({
                content: `${SETUP_EMOJIS.ERROR} Failed to toggle bosses ping. Please try again.`,
                ephemeral: true
            });
        }
    },

    async showWorldBossDisableConfirmation(interaction, roleType) {
        const roleTypeDisplay = roleType === 'bosses' ? 'Boss Ping Roles' : 'General Ping Role';

        const embed = new EmbedBuilder()
            .setColor('#ff9900')
            .setTitle(`${SETUP_EMOJIS.DISABLE_WARNING} Disable ${roleTypeDisplay}?`)
            .setDescription(`You have existing ${roleType} roles configured. What would you like to do with them?`)
            .addFields([
                {
                    name: 'Options',
                    value: '• **Keep Roles**: Disable pinging but keep role configurations\n• **Delete Roles**: Remove roles from Discord and database',
                    inline: false
                }
            ])
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`disable_worldboss_${roleType}_keep`)
                    .setLabel(`${SETUP_EMOJIS.KEEP_ROLES} Keep Roles (Disable Only)`)
                    .setStyle(ButtonStyle.Primary),
                new ButtonBuilder()
                    .setCustomId(`disable_worldboss_${roleType}_delete`)
                    .setLabel(`${SETUP_EMOJIS.DELETE_ROLES} Delete Roles`)
                    .setStyle(ButtonStyle.Danger),
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.CANCEL} Cancel`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });

        this.updateSession(interaction.user.id, {
            step: 'disable_worldboss_confirmation',
            roleType: roleType
        });
    },

    async disableWorldBossRoles(interaction, roleType, deleteRoles) {
        try {
            // Get current configuration
            let config = await configService.getServerConfig(interaction.guild.id);

            if (!config?.worldBossAlert) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} No world boss configuration found.`,
                    embeds: [],
                    components: []
                });
            }

            const roleTypeDisplay = roleType === 'bosses' ? 'Boss Ping Roles' : 'General Ping Role';

            // Initialize counters outside the if block
            let deletedCount = 0;
            let failedCount = 0;

            if (deleteRoles) {
                // Show progress and delete roles
                const progressEmbed = new EmbedBuilder()
                    .setColor('#ff9900')
                    .setTitle(`${SETUP_EMOJIS.PROGRESS} Disabling ${roleTypeDisplay}`)
                    .setDescription('Please wait while I disable the feature and delete the roles...')
                    .addFields([
                        { name: '⚙️ Status', value: '🔄 Deleting roles...', inline: true }
                    ])
                    .setTimestamp();

                await interaction.update({
                    embeds: [progressEmbed],
                    components: []
                });

                const rolesToDelete = this.getWorldBossRolesToDelete(config, roleType);

                for (const roleId of rolesToDelete) {
                    try {
                        const role = interaction.guild.roles.cache.get(roleId);
                        if (role) {
                            await role.delete('Disabled via setup command');
                            deletedCount++;
                            console.log(chalk.green(`${SETUP_EMOJIS.SUCCESS} Deleted role: ${role.name} (${roleId})`));
                        }
                    } catch (error) {
                        failedCount++;
                        console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Failed to delete role ${roleId}:`), error);
                    }
                }

                // Remove roles from config and disable
                console.log(chalk.cyan(`🔧 About to remove ${roleType} roles from config...`));
                this.removeWorldBossRolesFromConfig(config, roleType);
                console.log(chalk.green(`✅ Finished removing ${roleType} roles from config`));
            } else {
                // Just disable without deleting
                console.log(chalk.blue(`🔧 Disabling ${roleTypeDisplay} without deleting roles`));
            }

            // Disable the feature
            if (roleType === 'bosses') {
                config.worldBossAlert.bossesPingEnabled = false;
            } else {
                config.worldBossAlert.generalPingEnabled = false;
            }

            // Save to database
            await configService.saveServerConfig(interaction.guild.id, config);

            // Show completion message
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} ${roleTypeDisplay} Disabled`)
                .setDescription(`Successfully disabled ${roleTypeDisplay.toLowerCase()}.`)
                .setTimestamp();

            if (deleteRoles) {
                embed.addFields([
                    {
                        name: 'Summary',
                        value: [
                            `${SETUP_EMOJIS.DELETED} Deleted: ${deletedCount} roles`,
                            failedCount > 0 ? `${SETUP_EMOJIS.FAILED} Failed: ${failedCount} roles` : `${SETUP_EMOJIS.ALL_SUCCESS} All roles deleted successfully`,
                            `${SETUP_EMOJIS.DISABLED} ${roleTypeDisplay} disabled in configuration`
                        ].join('\n'),
                        inline: false
                    }
                ]);
            } else {
                embed.addFields([
                    {
                        name: 'Summary',
                        value: [
                            `${SETUP_EMOJIS.KEEP} Roles kept in Discord`,
                            `${SETUP_EMOJIS.DISABLED} ${roleTypeDisplay} disabled in configuration`,
                            `${SETUP_EMOJIS.INFO} You can re-enable this feature anytime`
                        ].join('\n'),
                        inline: false
                    }
                ]);
            }

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('worldboss_bosses_pings')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to Boss Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error disabling world boss roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to disable world boss roles. Error: ${error.message}`,
                embeds: [],
                components: []
            });
        }
    },

    getWorldBossRolesToDelete(config, roleType) {
        const roleIds = [];
        const worldBossRoles = config.worldBossAlert.worldBossRoles || {};

        if (roleType === 'bosses') {
            // Get all boss-specific roles
            const bossKeys = ['baruto', 'baizen', 'force', 'begeta', 'alien', 'don', 'protagonist', 'beach_ziru', 'beach_vermillion'];
            bossKeys.forEach(key => {
                if (worldBossRoles[key]) {
                    roleIds.push(worldBossRoles[key]);
                }
            });
        } else if (roleType === 'general') {
            // Get general role
            if (worldBossRoles.general) {
                roleIds.push(worldBossRoles.general);
            }
        }

        return roleIds.filter(id => id && typeof id === 'string');
    },

    removeWorldBossRolesFromConfig(config, roleType) {
        if (!config.worldBossAlert?.worldBossRoles) return;

        if (roleType === 'bosses') {
            // Remove all boss-specific roles
            const bossKeys = ['baruto', 'baizen', 'force', 'begeta', 'alien', 'don', 'protagonist', 'beach_ziru', 'beach_vermillion'];
            bossKeys.forEach(key => {
                delete config.worldBossAlert.worldBossRoles[key];
            });
        } else if (roleType === 'general') {
            // Remove general role
            delete config.worldBossAlert.worldBossRoles.general;
        }
    },

    // OLD FUNCTION - REPLACED WITH NEW BOSS PING SYSTEM
    async createWorldBossRoles_OLD(interaction) {
        try {
            // Check if bot has permission to create roles
            if (!interaction.guild.members.me.permissions.has(PermissionFlagsBits.ManageRoles)) {
                return await interaction.update({
                    content: `${SETUP_EMOJIS.ERROR} I don't have permission to create roles. Please give me the "Manage Roles" permission.`,
                    embeds: [],
                    components: []
                });
            }

            await interaction.update({
                content: `${SETUP_EMOJIS.LOADING} Creating world boss roles... This may take a moment.`,
                embeds: [],
                components: []
            });

            // Create World 1 and World 2 roles
            const world1Role = await interaction.guild.roles.create({
                name: 'World 1 Boss Ping',
                color: '#00ff00',
                reason: 'Created by setup command for world boss alerts'
            });

            const world2Role = await interaction.guild.roles.create({
                name: 'World 2 Boss Ping',
                color: '#ff0000',
                reason: 'Created by setup command for world boss alerts'
            });

            // Save to database
            let config = await configService.getServerConfig(interaction.guild.id);
            if (!config) {
                config = {
                    serverId: interaction.guild.id,
                    name: interaction.guild.name,
                    worldBossAlert: {
                        enabled: false,
                        worldBossRoles: {
                            world1: world1Role.id,
                            world2: world2Role.id
                        }
                    }
                };
            } else {
                if (!config.worldBossAlert) {
                    config.worldBossAlert = {
                        enabled: false,
                        worldBossRoles: {
                            world1: world1Role.id,
                            world2: world2Role.id
                        }
                    };
                } else {
                    if (!config.worldBossAlert.worldBossRoles) {
                        config.worldBossAlert.worldBossRoles = {};
                    }
                    config.worldBossAlert.worldBossRoles.world1 = world1Role.id;
                    config.worldBossAlert.worldBossRoles.world2 = world2Role.id;
                }
            }
            await configService.saveServerConfig(interaction.guild.id, config);

            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle(`${SETUP_EMOJIS.SUCCESS} World Boss Roles Created`)
                .setDescription('Successfully created world boss ping roles!')
                .addFields([
                    {
                        name: 'Created Roles',
                        value: `🌍 World 1: <@&${world1Role.id}>\n🌍 World 2: <@&${world2Role.id}>`,
                        inline: false
                    }
                ])
                .setTimestamp();

            const row = new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId('setup_back')
                        .setLabel(`${SETUP_EMOJIS.BACK} Back to World Boss Setup`)
                        .setStyle(ButtonStyle.Secondary)
                );

            await interaction.editReply({ embeds: [embed], components: [row] });

        } catch (error) {
            console.error(chalk.red(`${SETUP_EMOJIS.ERROR} Error creating world boss roles:`), error);
            await interaction.editReply({
                content: `${SETUP_EMOJIS.ERROR} Failed to create world boss roles. Please check my permissions and try again.`,
                embeds: [],
                components: []
            });
        }
    },

    // OLD FUNCTION - REPLACED WITH NEW BOSS PING SYSTEM  
    async showExistingWorldBossRoles_OLD(interaction) {
        const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`${SETUP_EMOJIS.ASSIGN} Assign Existing World Boss Roles`)
            .setDescription('This feature is coming soon! For now, please use the "Create World Boss Roles" option.')
            .setTimestamp();

        const row = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId('setup_back')
                    .setLabel(`${SETUP_EMOJIS.BACK} Back`)
                    .setStyle(ButtonStyle.Secondary)
            );

        await interaction.update({ embeds: [embed], components: [row] });
    }
};


setInterval(() => {
    const now = Date.now();
    for (const [userId, session] of setupSessions.entries()) {
        if (now - session.startTime > SESSION_TIMEOUT) {
            setupSessions.delete(userId);
        }
    }
}, 60000); 
