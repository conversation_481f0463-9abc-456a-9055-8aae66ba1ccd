
module.exports = {

    // General Configs
    prefix: `$`, //Add a prefix for message commands, or leave it blank to disable message commands.
    ownerID: `565854774612983808`, //your id (cannot add multiple)
    token: 'MTM2NDI1Mjk3NDM5Mzg1MTk3Nw.GKPRGT.eCteeWcMHLiKgB3nKM8mfpa2KasJh6hrlvi0Ew',
    tokenDungeon: `MTM1NDI4NTY1Mzk5ODgyOTYzMA.GTx3yy.9ZAUbW2ZjUGa6XNPoS4e9gzcXczbipWBVSYqFU`,
    tokenHunter: `MTM2MDUwNDQ4NjM2NzMzNDUxMg.GzDbzi.Jz2urPtRpu37Nak1ExfRQIXypS0lASyc45cTpM`, // Add your hunter key bot token here
    guildId: ``, // Enter a guild ID for guild-specific commands, or leave it blank to load slash commands globally.

    developerTeam: {
        memberIDs: [], // add the user IDs of the developer team (can add multiple)
        roleIDs: [],   // add the role IDs of the developer team (can add multiple)
    },

    // Bot Embed Configs
    EmbedConfig: {
        embedcolor: `#164ee9`, //default embed color 
    },

    // Cooldown Configs
    CooldownConfig: {
        defaultCooldown: 5, //default cooldown for commands (it's 5 seconds here)
        autoDeleteCooldownMsg: 10 //auto delete the response which the bot says "you're on cooldown" (it's 10 seconds here)
    },

    // Text Config (Used in help (command))
    TextConfig: {
        color: 'white', //white, teal, pink, light_blue, gold, yellowish_green, red, dark_gray (leave blank for normal)
        background_color: '', //cream_white, light_gray60, blurple, light_gray55, gray45, gray40, rust_brown, blueish_black (leave blank for normal)
        bold: true, // Toggle bold text color
        underline: false, // Toggle bold text color
    },

    ImageConfig: {
        JEYY_API_KEY: '6KR3AE1L6GRJED1M64P3IE1J70O3G.89NN8T0.HqIGV-I1yM-lvCmiUm317A',
        OCR_API_KEY: 'K82535073388957',
        LOADING_EMOJI: '<a:loading_circle_RB:1392930612754513950>',
        AvoidImageEdit: ["565854774612983808", "1364252974393851977", "913390209352212481"], //These users can be avoided from image edit by others
        BypassImageEdit: ["565854774612983808"], //These users can bypass avoided users from above list
        ResponseForAvoid: 'Sorry, I cannot generate an image for this user.',
    },

    // Automod Config
    AutomodConfig: {
        enabled: false, // Enable/disable automod globally
        logChannelId: '1366457560143761591', // Channel ID to log deleted messages (leave blank to disable logging)
        alertChannelId: '1380489866156048494', // Channel for immediate alerts (high severity violations)
        maxWarnings: 3, // Maximum warnings before timeout
        muteDuration: 60 * 60 * 1000, // Timeout duration in milliseconds (1 hour)
        dmUsers: true, // Send DM notifications to users when their messages are deleted
        exemptRoles: ['1364227105327415326'], // Role IDs that are exempt from automod
        exemptChannels: [], // Channel IDs where automod is disabled
        trustedRoles: ['1366460181218852917'], // Role IDs that have reduced automod restrictions

        // Advanced settings
        enableAdvancedLogging: true, // Enable detailed logging with anonymized content
        enableContextAnalysis: true, // Analyze surrounding messages for context
        enableSpamDetection: true, // Detect spam patterns
        enableLinkFiltering: true, // Filter suspicious links
        enableImageFiltering: true, // Basic image content filtering
        enableRateLimiting: true, // Rate limit users

        // Severity-based punishment system
        severitySystem: {
            enabled: true,
            lowSeverity: {
                action: 'warn', // warn, delete, timeout
                duration: 0, // timeout duration in minutes (0 = no timeout)
                warningWeight: 1 // how much this adds to warning count
            },
            mediumSeverity: {
                action: 'timeout',
                duration: 10, // 10 minutes
                warningWeight: 2
            },
            highSeverity: {
                action: 'timeout',
                duration: 60, // 1 hour
                warningWeight: 3
            },
            criticalSeverity: {
                action: 'timeout',
                duration: 1440, // 24 hours
                warningWeight: 5
            }
        },

        // Detection settings with severity levels
        detectRacistContent: { enabled: true, severity: 'high' },
        detectDeathThreats: { enabled: true, severity: 'critical' },
        detectHateSpeech: { enabled: true, severity: 'high' },
        detectProfanity: { enabled: true, severity: 'low' },
        detectSexualContent: { enabled: true, severity: 'medium' },
        detectToxicity: { enabled: true, severity: 'medium' },
        detectSpam: { enabled: true, severity: 'low' },
        detectExcessiveCaps: { enabled: true, severity: 'low' },
        detectSuspiciousLinks: { enabled: true, severity: 'medium' },
        detectDiscordInvites: { enabled: true, severity: 'medium' },
        detectZalgoText: { enabled: true, severity: 'low' },
        detectRepeatedText: { enabled: true, severity: 'low' },

        // Spam detection settings
        spamDetection: {
            maxDuplicateMessages: 3, // Max identical messages in timeframe
            duplicateTimeframe: 30000, // 30 seconds
            maxMessagesPerMinute: 10, // Max messages per minute
            maxCapsPercentage: 70, // Max percentage of caps in message
            minMessageLength: 10, // Minimum length for caps detection
            maxEmojisPerMessage: 5, // Max emojis per message
            maxMentionsPerMessage: 3 // Max mentions per message
        },

        // Link filtering settings
        linkFiltering: {
            whitelist: [], // Whitelisted domains
            blacklist: ['bit.ly', 'tinyurl.com'], // Blacklisted domains
            blockShorteners: true, // Block URL shorteners
            blockSuspiciousExtensions: true, // Block suspicious file extensions
            suspiciousExtensions: ['.exe', '.scr', '.bat', '.cmd', '.pif']
        },

        // Profanity filtering settings
        profanityFiltering: {
            strictMode: false, // Strict mode catches more variations
            allowedChannels: [], // Channels where profanity is allowed
            bypassRoles: [], // Roles that can bypass profanity filter
            filterLevel: 'medium', // low, medium, high, strict
            replaceWithSymbols: true, // Replace with *** in logs
            warnOnMildProfanity: false // Warn on mild profanity or just delete
        },

        // Toxicity detection settings
        toxicityDetection: {
            detectInsults: true, // Personal attacks and insults
            detectAggression: true, // Aggressive language
            detectBullying: true, // Bullying behavior
            detectHarassment: true, // Harassment patterns
            contextSensitive: true // Consider message context
        },

        // Custom patterns (server owners can add their own)
        customPatterns: {
            enabled: false,
            patterns: [] // Array of {pattern: string, severity: string, description: string}
        },

        // Appeal system
        appealSystem: {
            enabled: true,
            appealChannelId: '', // Channel where users can appeal
            appealCooldown: 24 * 60 * 60 * 1000 // 24 hours between appeals
        }
    },

    // Beta Server Config
    BetaServerConfig: {
        enabled: true, // Enable/disable beta server restrictions
        serverIds: ['923445406849990728',
            '1150767996625768499',
            '1180144893105029202',
            '1203957066406830120',
            '1239852521531637772',
            '1299736472919740478',
            '1345907653154574346',
            '1346500406641033277',
            '1353186342682497124',
            '1356758177726861363',
            '1359222526747873371',
            '1360726183309738326',
            '1361817408699895818',
            '1362356687092191442',
            '1362796685188796476',
            '1364152374876176394',
            '1366859625177288847',
            '1368662745578475691',
            '1369337336076435477',
            '1370343058733928578',
            '1371212189448278087',
            '1371332839379042324',
            '1372247020135518309',
            '1374046574270746777',
            '1376879294885789848',
            '1377292323456549026',
            '1377699403917426902',
            '1377911598932295750',
            '1380593462969237515',
            '1295396188958490654',
            '1381574580505677875',
            '1293579279522598975',
            '1182316861497544747',
            '1153088100310986834',
            '1361464129965265019',
            '295992269004079114',
            '1388922057831747694',
            '1040047273779920927',
            '1360952828826222602',
	    '1387080145218699426'],
    },

    // Responses Config
    ResponsesConfig: {
        botowneronly: {
            reply: `This command is restricted to the bot owner.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        serverowneronly: {
            reply: `This command is restricted to the server owner.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        developerteamonly: {
            reply: `This command is restricted to the developer team.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        betaserveronly: {
            reply: `This command is restricted to Beta Servers Only.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        nsfw: {
            reply: `This command can only be used in NSFW channels.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        memberpermissions: {
            reply: `You don't have the necessary permissions to use this command.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        botpermissions: {
            reply: `I don't have the necessary permissions to execute this command.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        requiredroles: {
            reply: `You don't have the required role(s) to use this command.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        requiredchannels: {
            reply: `This command can only be used in specific channels.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        },
        alloweduserids: {
            reply: `You are not allowed to use this command.`,
            autoDelete: 10, // in seconds
            shouldAutoDelete: true
        }
    },

    // Infernal Castle Monitor Config
    InfernalConfig: {
        enabled: true, // Enable/disable infernal castle monitoring
        timezone: 'Asia/Kolkata', // Timezone for cron jobs
        monitoring: {
            startMinute: 15, // Start monitoring at this minute of every hour
            endMinute: 26,   // End monitoring at this minute of every hour
        },
        storageMode: 'memory', // 'file', 'memory', 'discord'
        targetChannelId: '1372921982483435581', // Channel to post infernal updates
        sourceChannelIds: ['1381601141959295082', '1371927743037440141', '1329517975950065847', '1372822081921749073'], // Channels to monitor for floor information
        // Add your new formatted message channel ID here if needed
        floorRange: {
            min: 30, // Don't log floors below this number
            max: 70  // Don't log floors above this number
        },
        channelThresholds: {
            '1381601141959295082': 1, // Regular channel - each report counts as 1
            '1371927743037440141': 1, // Regular channel - each report counts as 1
            '1329517975950065847': 3,  // Special channel - requires 3 users to count as 1 report
            '1372822081921749073': 1  // Regular channel - each report counts as 1
        },
        roleIds: {
            general: '1372921375005737003',
            monarch: '1373306924115824740',
            daeIn: '1374780329864204330'
        }
    },

    // Hunter Key Config
    HunterKeyConfig: {
        enabled: true, // Enable/disable hunter key monitoring
        monitorChannelId: '1347345342567415808', // Channel to monitor for hunter key links
        postChannelId: '1377990535083720807', // Channel to post hunter key links directly (Hunter Key 1)
        farmPostInterval: 6, // Minutes between farm link posts
        farmLink: 'Hunter key Farm https://www.roblox.com/share?code=102a210120e93c46ad32d1bc0e00a7b5&type=Server',
        excludeWords: ['fake'], // Words that will exclude a message from being posted
        keywords: ['hurricane keys', 'hurricanes keys', 'king slime', 'slime', 'slime king', 'rimuru', 'slime', 'hurricane key dungeon', 'hurricanes', 'key', 'keys', 'hurricane'], // Keywords to look for
        webhookConfig: {
            name: 'RankBreaker',
            avatar: 'https://cdn.discordapp.com/icons/1362356687092191442/25b53ae035c74c9d6edcf8ca11dfc205.webp?size=1024'
        }
    },

    // Desert Farm Config
    DesertFarmConfig: {
        enabled: true, // Enable/disable desert farm monitoring
        monitorChannelId: '1347345342567415808', // Channel to monitor for desert farm links
        postChannelId: '1389952754033627166', // Channel to post desert farm links
        farmPostInterval: 6, // Minutes between farm link posts
        farmLink: 'Desert Farm https://www.roblox.com/share?code=102a210120e93c46ad32d1bc0e00a7b5&type=Server',
        excludeWords: ['fake'], // Words that will exclude a message from being posted
        keywords: ['desert', 'infinite', 'inf'], // Keywords to look for
        webhookConfig: {
            name: 'RankBreaker',
            avatar: 'https://cdn.discordapp.com/icons/1362356687092191442/25b53ae035c74c9d6edcf8ca11dfc205.webp?size=1024'
        }
    },

    // Roblox Config
    RobloxConfig: {
        bloxlinkChannelId: '1347744858290524203', // Channel ID where Bloxlink commands will be sent
        bloxlinkBotId: '426537812993638400' // Bloxlink bot ID
    }

}
