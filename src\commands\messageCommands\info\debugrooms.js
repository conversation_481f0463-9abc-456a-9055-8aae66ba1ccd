module.exports = {
    name: 'debugrooms',
    description: 'Debug room detection system',
    usage: 'debugrooms',
    category: 'info',
    aliases: [],
    cooldown: 5,
    OwnerOnly: true,
    ServerOwnerOnly: false,
    DeveloperTeamOnly: false,
    nsfw: false,
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,

    async execute(message, args) {
        try {
            const dungeonAlert = message.client.dungeonAlert;
            
            if (!dungeonAlert) {
                return message.reply('❌ DungeonAlert instance not found');
            }

            // Show stored dungeon messages
            dungeonAlert.getStoredDungeonMessages();

            // Test room extraction
            const testMessages = [
                '# 6 ROOMS',
                '# 8 ROOMS #',
                'Found 4 rooms',
                'rooms: 5',
                'not a room message'
            ];

            message.reply('🔍 **Room Detection Debug**\n\nCheck console for stored messages.\n\n**Testing room extraction:**');

            for (const testMsg of testMessages) {
                const roomCount = dungeonAlert.extractRoomCount(testMsg);
                console.log(`[Debug] "${testMsg}" -> ${roomCount ? `${roomCount} rooms` : 'No match'}`);
            }

            message.channel.send('✅ Debug complete - check console logs');

        } catch (error) {
            console.error('[DebugRooms] Error:', error);
            message.reply('❌ Error during debug: ' + error.message);
        }
    }
};