const { SlashCommandBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('roblox')
        .setDescription('Roblox-related commands'),
    name: 'roblox',
    category: 'Misc',
    aliases: [],
    cooldown: 5,
    usage: 'roblox <subcommand>',
    description: 'Roblox-related commands',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 0,
    nsfw: false,
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'both',

    // Map to store subcommands
    subcommands: new Map(),

    // Build subcommands and populate subcommands map
    registerSubcommands() {
        const folder = __dirname;
        const files = fs.readdirSync(folder)
            .filter(f => f !== 'index.js' && f.endsWith('.js'));
        
        for (const file of files) {
            try {
                const mod = require(path.join(folder, file));
                // Each module must export .data as a SlashCommandSubcommandBuilder
                if (mod.data && mod.data.name) {
                    this.data.addSubcommand(mod.data);
                    this.subcommands.set(mod.data.name, mod);
                    console.log(`✅ Loaded roblox subcommand: ${mod.data.name}`);
                }
            } catch (error) {
                console.error(`❌ Error loading roblox module ${file}:`, error);
            }
        }
    },

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();
        const subcommandModule = this.subcommands.get(subcommand);
        
        if (!subcommandModule) {
            return await interaction.reply({
                content: `❌ Unknown subcommand: ${subcommand}`,
                ephemeral: true
            });
        }

        try {
            await subcommandModule.execute(interaction);
        } catch (error) {
            console.error(`Error executing roblox subcommand ${subcommand}:`, error);
            
            const errorMessage = '❌ There was an error executing this command!';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        }
    }
};

// Register subcommands when the module is loaded
module.exports.registerSubcommands();
