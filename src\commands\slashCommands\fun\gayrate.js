const { SlashCommandBuilder } = require('discord.js');
const config = require('../../../../config/config.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('gayrate')
        .setDescription('Shows the gay rate of a user.')
        .addUserOption(option => 
            option.setName('user')
                .setDescription('The user to check the gay rate of.')
                .setRequired(false)
        ),
    name: 'gayrate',
    category: 'Fun',
    aliases: [],
    cooldown: 10,
    usage: '[user]',
    description: 'Shows the gay rate of a user.',
    memberpermissions: [],
    botpermissions: [],
    requiredroles: [],
    requiredchannels: [],
    alloweduserids: [],
    minargs: 0,
    maxargs: 1,
    nsfw: false,
    BotOwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'both',

    async execute(interaction) {

        const specialUserIds = ['565854774612983808', '913390209352212481']; //place here ids of users you want gay rate 0%
        const smallUsersIds = ['1231217991929036842', '900433233055985684', '712907863328161842']; //place here ids of users you want gay rate 100%

        const user = interaction.options.getUser('user') || interaction.user;

        // Handle both guild and user app contexts
        let displayName;
        let member = null;

        if (interaction.guild) {
            // Guild context - try to fetch member
            try {
                member = await interaction.guild.members.fetch(user.id);
                displayName = member.displayName;
            } catch (error) {
                // User not in guild, fall back to username
                displayName = user.displayName || user.username;
            }
        } else {
            // User app context - use user's display name or username
            displayName = user.displayName || user.username;
        }

        let gayRate;
        
        if (specialUserIds.includes(user.id)) {
            // Special users get 0% gay rate
            gayRate = 0;
        } else if (smallUsersIds.includes(user.id)) {
            // Small users get 100% gay rate
            gayRate = 100;
        } else {
            // Normal users get random rate between 0-100
            gayRate = Math.floor(Math.random() * 101);
        }

        // Create visual representation with emojis
        let emoji;
        if (gayRate === 0) {
            // Use gigachad emoji for 0% gay rate
            emoji = '<:gigachad_RB:1392151987444777041>';
        } else {
            // Use random rainbow emoji for other rates
            const rainbowEmojis = ['🏳️‍🌈', '🌈', '💖', '💜', '💙', '💚', '💛', '🧡', '❤️'];
            emoji = rainbowEmojis[Math.floor(Math.random() * rainbowEmojis.length)];
        }

        const responseMessage = `**${displayName}** is **${gayRate}%** gay! ${emoji}`;
        await interaction.reply({ content: responseMessage });
    },
};