module.exports = {
    name: 'messageUpdate',
    async execute(oldMessage, newMessage) {
        // Only process for the rooms enabled server to reduce noise
        if (newMessage.guild?.id !== '923445406849990728') {
            return;
        }

        // Handle room detection for edited messages (for non-bot messages)
        if (!newMessage.author.bot && newMessage.guild) {
            try {
                // Get dungeonAlert instance from client
                const dungeonAlert = newMessage.client.dungeonAlert;
                if (dungeonAlert) {
                    await dungeonAlert.handlePotentialRoomMessage(newMessage);
                }
            } catch (error) {
                console.error('[MessageUpdate] Error handling room message:', error);
            }
        }
    },
};