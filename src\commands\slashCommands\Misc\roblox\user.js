const { SlashCommandSubcommandBuilder, EmbedBuilder } = require('discord.js');
const https = require('https');
const path = require('path');

// build an absolute path to config.js from current file
const configPath = path.resolve(__dirname, '../../../../../config/config.js');
const config = require(configPath);


module.exports = {
    data: new SlashCommandSubcommandBuilder()
        .setName('user')
        .setDescription('Get Roblox user information')
        .addStringOption(option =>
            option.setName('username')
                .setDescription('Roblox username to lookup')
                .setRequired(false)
        )
        .addUserOption(option =>
            option.setName('discorduser')
                .setDescription('Discord user to get Roblox info for')
                .setRequired(false)
        ),
    name: 'user',
    category: 'Misc',

    async execute(interaction) {
        const username = interaction.options.getString('username');
        const discordUser = interaction.options.getUser('discorduser');

        // Check if at least one option is provided
        if (!username && !discordUser) {
            return await interaction.reply({
                content: '❌ Please provide either a Roblox username or a Discord user!',
                ephemeral: true
            });
        }

        // Check if both options are provided
        if (username && discordUser) {
            return await interaction.reply({
                content: '❌ Please provide either a Roblox username OR a Discord user, not both!',
                ephemeral: true
            });
        }

        await interaction.deferReply();

        try {
            let robloxUsername;

            if (discordUser) {
                // Show searching message
                await interaction.editReply({
                    content: '🔍 Searching user\'s roblox...'
                });

                // Get Roblox username from Discord user via Bloxlink
                robloxUsername = await this.getRobloxUsernameFromDiscord(discordUser);
                if (!robloxUsername) {
                    return await interaction.editReply({
                        content: `❌ Could not find Roblox account linked to ${discordUser.tag}. Make sure they have linked their account with Bloxlink.`
                    });
                }
            } else {
                robloxUsername = username;
            }

            // Get Roblox user information
            const userInfo = await this.getRobloxUserInfo(robloxUsername);
            if (!userInfo) {
                return await interaction.editReply({
                    content: `❌ Could not find Roblox user: ${robloxUsername}`
                });
            }

            // Create and send embed with user information
            const embed = await this.createUserEmbed(userInfo, discordUser);
            await interaction.editReply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in roblox user command:', error);
            await interaction.editReply({
                content: '❌ An error occurred while fetching user information. Please try again later.'
            });
        }
    },

    async getRobloxUsernameFromDiscord(discordUser) {
        return new Promise(async (resolve) => {
            try {
                console.log(`🔍 Searching for Roblox username for Discord user: ${discordUser.tag}`);

                // Use the channel ID from config
                const channelId = config.RobloxConfig.bloxlinkChannelId;

                // Unfortunately, user accounts cannot directly trigger slash commands for other bots via API
                // This is a Discord limitation for security reasons
                // We need to use an alternative approach

                console.log(`📤 Sending message to trigger Bloxlink command for user ${discordUser.id}`);

                // Send a message that might trigger Bloxlink's auto-detection
                // Some bots respond to mentions or specific formats
                const commandContent = `<@${config.RobloxConfig.bloxlinkBotId}> getinfo <@${discordUser.id}>`;

                await this.makeDiscordRequest(`channels/${channelId}/messages`, {
                    method: 'POST',
                    headers: {
                        'Authorization': config.tokenDungeon,
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    },
                    body: {
                        content: commandContent
                    }
                });

                console.log(`✅ Bloxlink mention command sent: ${commandContent}`);

                // Wait for Bloxlink response and parse it
                setTimeout(async () => {
                    try {
                        const messages = await this.makeDiscordRequest(`channels/${channelId}/messages?limit=10`);
                        
                        // Look for Bloxlink response
                        for (const message of messages) {
                            if (message.author.id === config.RobloxConfig.bloxlinkBotId && message.embeds && message.embeds.length > 0) {
                                const embed = message.embeds[0];

                                // Check if this embed contains components with Roblox information
                                if (embed.components) {
                                    for (const component of embed.components) {
                                        if (component.content && component.content.includes('@')) {
                                            // Extract username from content like "### @Captainakshan2019"
                                            const match = component.content.match(/###\s*@(\w+)/);
                                            if (match) {
                                                console.log(`✅ Found Roblox username: ${match[1]}`);
                                                return resolve(match[1]);
                                            }
                                        }
                                    }
                                }

                                // Also check embed description for username patterns
                                if (embed.description) {
                                    const descMatch = embed.description.match(/@(\w+)/);
                                    if (descMatch) {
                                        console.log(`✅ Found Roblox username in description: ${descMatch[1]}`);
                                        return resolve(descMatch[1]);
                                    }
                                }

                                // Check embed fields for Roblox username
                                if (embed.fields) {
                                    for (const field of embed.fields) {
                                        if (field.value && field.value.includes('@')) {
                                            const fieldMatch = field.value.match(/@(\w+)/);
                                            if (fieldMatch) {
                                                console.log(`✅ Found Roblox username in field: ${fieldMatch[1]}`);
                                                return resolve(fieldMatch[1]);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                        console.log('❌ No Bloxlink response found');
                        resolve(null);
                    } catch (error) {
                        console.error('Error parsing Bloxlink response:', error);
                        resolve(null);
                    }
                }, 3000); // Wait 3 seconds for Bloxlink to respond

            } catch (error) {
                console.error('Error getting Roblox username from Discord:', error);
                resolve(null);
            }
        });
    },

    async getRobloxUserInfo(username) {
        try {
            // First, get user ID from username
            const userSearchResponse = await this.makeRobloxRequest(`https://users.roblox.com/v1/users/search?keyword=${encodeURIComponent(username)}&limit=10`);

            if (!userSearchResponse.data || userSearchResponse.data.length === 0) {
                return null;
            }

            const userId = userSearchResponse.data[0].id;

            // Get detailed user information
            const userInfoResponse = await this.makeRobloxRequest(`https://users.roblox.com/v1/users/${userId}`);

            return userInfoResponse;
        } catch (error) {
            console.error('Error fetching Roblox user info:', error);
            return null;
        }
    },

    async createUserEmbed(userInfo, discordUser) {
        const embed = new EmbedBuilder()
            .setTitle(`🎮 Roblox User Information`)
            .setColor('#00D2FF')
            .setThumbnail(`https://www.roblox.com/headshot-thumbnail/image?userId=${userInfo.id}&width=420&height=420&format=png`)
            .addFields(
                { name: '👤 Username', value: userInfo.name || 'Unknown', inline: true },
                { name: '🆔 User ID', value: userInfo.id?.toString() || 'Unknown', inline: true },
                { name: '📅 Account Created', value: userInfo.created ? `<t:${Math.floor(new Date(userInfo.created).getTime() / 1000)}:F>` : 'Unknown', inline: false }
            )
            .setTimestamp()
            .setFooter({ text: 'RankBreaker • Roblox API', iconURL: 'https://cdn.discordapp.com/icons/1362356687092191442/25b53ae035c74c9d6edcf8ca11dfc205.webp' });

        if (userInfo.displayName && userInfo.displayName !== userInfo.name) {
            embed.addFields({ name: '📝 Display Name', value: userInfo.displayName, inline: true });
        }

        if (userInfo.description) {
            embed.addFields({ name: '📄 Description', value: userInfo.description.length > 1024 ? userInfo.description.substring(0, 1021) + '...' : userInfo.description, inline: false });
        }

        if (discordUser) {
            embed.addFields({ name: '🔗 Linked Discord User', value: `${discordUser.tag}`, inline: true });
        }

        return embed;
    },

    generateSessionId() {
        // Generate a random session ID (32 characters)
        return Array.from({length: 32}, () => Math.floor(Math.random() * 16).toString(16)).join('');
    },

    generateNonce() {
        // Generate a snowflake-like nonce
        return (BigInt(Date.now() - 1420070400000) << 22n).toString();
    },

    makeDiscordRequest(endpoint, options = {}) {
        return new Promise((resolve, reject) => {
            const defaultOptions = {
                hostname: 'discord.com',
                path: `/api/v10/${endpoint}`,
                method: 'GET',
                headers: {
                    'Authorization': config.tokenDungeon,
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
            };

            const requestOptions = { ...defaultOptions, ...options };

            const req = https.request(requestOptions, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const parsedData = JSON.parse(data);
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(parsedData);
                        } else {
                            console.error(`Discord API Error (${res.statusCode}):`, parsedData);
                            console.error(`Request details: ${requestOptions.method} ${requestOptions.hostname}${requestOptions.path}`);
                            console.error(`Headers:`, requestOptions.headers);
                            reject(new Error(`Discord API returned status code ${res.statusCode}: ${JSON.stringify(parsedData)}`));
                        }
                    } catch (error) {
                        reject(new Error(`Failed to parse Discord API response: ${error.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`Discord API request failed: ${error.message}`));
            });

            if (requestOptions.body) {
                req.write(JSON.stringify(requestOptions.body));
            }

            req.end();
        });
    },

    makeRobloxRequest(url) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            
            const options = {
                hostname: urlObj.hostname,
                path: urlObj.pathname + urlObj.search,
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            };

            const req = https.request(options, (res) => {
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const parsedData = JSON.parse(data);
                        if (res.statusCode >= 200 && res.statusCode < 300) {
                            resolve(parsedData);
                        } else {
                            console.error(`Roblox API Error (${res.statusCode}):`, parsedData);
                            reject(new Error(`Roblox API returned status code ${res.statusCode}`));
                        }
                    } catch (error) {
                        reject(new Error(`Failed to parse Roblox API response: ${error.message}`));
                    }
                });
            });

            req.on('error', (error) => {
                reject(new Error(`Roblox API request failed: ${error.message}`));
            });

            req.end();
        });
    }
};
