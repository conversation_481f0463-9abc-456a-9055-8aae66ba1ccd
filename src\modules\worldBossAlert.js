const fs = require('fs').promises;
const path = require('path');
const { AttachmentBuilder, EmbedBuilder } = require('discord.js');
const { createCanvas, loadImage } = require('canvas');
const chalk = require('chalk');
const configWrapper = require('../services/configWrapper');

class WorldBossAlert {
    constructor(client) {
        this.client = client;
        this.watchFolder = path.join(__dirname, '../monitors/worldboss_alerts');
        this.processedFiles = new Set();
        this.isWatching = false;
        this.sharedConfig = null; // Will be loaded dynamically
        this.bossColors = null; // Will be loaded dynamically
    }

    // Helper method to get fresh shared configuration
    async getFreshSharedConfig() {
        return await configWrapper.getSharedConfig();
    }

    async ensureWatchFolder() {
        try {
            await fs.mkdir(this.watchFolder, { recursive: true });
        } catch (error) {
            // Folder already exists
        }
    }

    async createWorldBossImage(worldBossData) {
        try {
            // Dynamic canvas height based on number of worlds
            const worldCount = Object.keys(worldBossData.worldBosses).length;
            const canvasHeight = 500 + (worldCount > 2 ? (worldCount - 2) * 100 : 0);
            const canvas = createCanvas(800, canvasHeight);
            const ctx = canvas.getContext('2d');

            const primaryColor = '#aa44ff';

            // Enhanced gradient background
            const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
            gradient.addColorStop(0, '#0f1016');
            gradient.addColorStop(0.5, '#1a1b26');
            gradient.addColorStop(1, '#0f1016');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Enhanced particle effect
            this.drawEnhancedMagicalParticles(ctx, primaryColor, canvas);

            // Draw stylized border
            this.drawSoloLevelingBorder(ctx, primaryColor, canvas);

            // Draw header with subtle glow
            this.drawWorldBossHeader(ctx, canvas.width / 2, 60, primaryColor);

            // Draw world boss hexagonal icon
            await this.drawWorldBossIcon(ctx, canvas.width / 2, 140, primaryColor);

            // Dynamic status window sizing - match dungeon alert spacing
            const panelX = 40;
            const panelY = 200;  // Changed from 190 to 200 to match dungeon alert spacing
            const panelWidth = canvas.width - 80;
            const panelHeight = canvasHeight - 250;  // Adjusted accordingly
            this.drawStatusWindow(ctx, panelX, panelY, panelWidth, panelHeight, primaryColor);

            // Draw world boss info
            let y = 250;  // Adjusted to match new panel position
            const leftMargin = 70;

            // Calculate spacing based on whether worlds have islands
            let totalHeight = 0;
            const worlds = Object.keys(worldBossData.worldBosses).sort();
            worlds.forEach(worldNum => {
                const worldNumber = parseInt(worldNum);
                const worldData = worldBossData.worldBosses[worldNum];
                // World 1 and 2 need less space (no island info)
                if (worldNumber === 1 || worldNumber === 2) {
                    totalHeight += 100; // Just world + boss
                } else if (worldData.island) {
                    totalHeight += 140; // World + island + boss
                } else {
                    totalHeight += 100; // Just world + boss
                }
            });

            const worldSpacing = Math.max(120, panelHeight / (worldCount + 0.5));
            const lineSpacing = 40;
            worlds.forEach((worldNum, index) => {
                const worldData = worldBossData.worldBosses[worldNum];
                const currentY = y + (index * worldSpacing);
                const worldNumber = parseInt(worldNum);

                // World header with gradient
                ctx.font = 'bold 36px "Whitney", Arial, sans-serif';
                const worldGradient = ctx.createLinearGradient(leftMargin, currentY, leftMargin + 200, currentY);
                worldGradient.addColorStop(0, '#88aaff');
                worldGradient.addColorStop(1, '#55ccff');
                ctx.fillStyle = worldGradient;
                ctx.textAlign = 'left';
                ctx.fillText(`World ${worldNum}`, leftMargin, currentY);

                // For World 1 and 2, skip island info and only show boss
                if (worldNumber === 1 || worldNumber === 2) {
                    // Boss info directly without island
                    ctx.font = '32px "Whitney", Arial, sans-serif';
                    ctx.fillStyle = '#88aaff';
                    ctx.fillText(`Boss:`, leftMargin + 20, currentY + lineSpacing);

                    const bossLabelWidth = ctx.measureText('Boss:').width;
                    ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
                    ctx.shadowBlur = 8;
                    ctx.fillStyle = '#ffffff';
                    const bossText = worldData.level ? `${worldData.boss} (Lv. ${worldData.level})` : worldData.boss;
                    ctx.fillText(` ${bossText}`, leftMargin + 20 + bossLabelWidth, currentY + lineSpacing);
                    ctx.shadowBlur = 0;
                } else {
                    // For other worlds, show island info if available
                    if (worldData.island) {
                        // Island info
                        ctx.font = '32px "Whitney", Arial, sans-serif';
                        ctx.fillStyle = '#88aaff';
                        ctx.fillText(`Island:`, leftMargin + 20, currentY + lineSpacing);

                        const islandLabelWidth = ctx.measureText('Island:').width;
                        ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
                        ctx.shadowBlur = 8;
                        ctx.fillStyle = '#ffffff';
                        ctx.fillText(` ${worldData.island}`, leftMargin + 20 + islandLabelWidth, currentY + lineSpacing);
                        ctx.shadowBlur = 0;

                        // Boss info with level
                        ctx.font = '32px "Whitney", Arial, sans-serif';
                        ctx.fillStyle = '#88aaff';
                        ctx.fillText(`Boss:`, leftMargin + 20, currentY + (lineSpacing * 2));

                        const bossLabelWidth = ctx.measureText('Boss:').width;
                        ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
                        ctx.shadowBlur = 8;
                        ctx.fillStyle = '#ffffff';
                        const bossText = worldData.level ? `${worldData.boss} (Lv. ${worldData.level})` : worldData.boss;
                        ctx.fillText(` ${bossText}`, leftMargin + 20 + bossLabelWidth, currentY + (lineSpacing * 2));
                        ctx.shadowBlur = 0;
                    } else {
                        // Just boss info without island
                        ctx.font = '32px "Whitney", Arial, sans-serif';
                        ctx.fillStyle = '#88aaff';
                        ctx.fillText(`Boss:`, leftMargin + 20, currentY + lineSpacing);

                        const bossLabelWidth = ctx.measureText('Boss:').width;
                        ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
                        ctx.shadowBlur = 8;
                        ctx.fillStyle = '#ffffff';
                        const bossText = worldData.level ? `${worldData.boss} (Lv. ${worldData.level})` : worldData.boss;
                        ctx.fillText(` ${bossText}`, leftMargin + 20 + bossLabelWidth, currentY + lineSpacing);
                        ctx.shadowBlur = 0;
                    }
                }

                // Add boss level indicator circle
                if (worldData.level) {
                    const circleX = canvas.width - 100;
                    const circleY = currentY + lineSpacing;
                    ctx.beginPath();
                    ctx.arc(circleX, circleY, 20, 0, Math.PI * 2);
                    const levelGradient = ctx.createRadialGradient(circleX, circleY, 0, circleX, circleY, 20);
                    levelGradient.addColorStop(0, '#ff4444');
                    levelGradient.addColorStop(1, '#aa0000');
                    ctx.fillStyle = levelGradient;
                    ctx.fill();
                    ctx.font = 'bold 20px "Whitney", Arial, sans-serif';
                    ctx.fillStyle = '#ffffff';
                    ctx.textAlign = 'center';
                    ctx.fillText(worldData.level, circleX, circleY + 7);
                    ctx.textAlign = 'left';
                }
            });

            // Enhanced watermark
            ctx.font = 'bold 20px "Whitney", Arial, sans-serif';
            ctx.fillStyle = primaryColor.replace(')', ', 0.9)').replace('rgb', 'rgba');
            ctx.textAlign = 'center';
            ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
            ctx.shadowBlur = 5;
            ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 25);
            ctx.shadowBlur = 0;

            return canvas.toBuffer('image/png');
        } catch (error) {
            console.error(chalk.red('❌ Error creating world boss image:'), error);
            return null;
        }
    }

    drawEnhancedMagicalParticles(ctx, color, canvas) {
        const particleCount = 150;

        // Base particles
        for (let i = 0; i < particleCount; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const size = Math.random() * 3 + 1;
            const alpha = Math.random() * 0.6 + 0.2;

            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            ctx.fillStyle = color.replace(')', `, ${alpha})`).replace('rgb', 'rgba');
            ctx.fill();
        }

        // Glowing particles
        for (let i = 0; i < 30; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const size = Math.random() * 5 + 3;

            ctx.beginPath();
            ctx.arc(x, y, size, 0, Math.PI * 2);
            const gradient = ctx.createRadialGradient(x, y, 0, x, y, size * 1.5);
            gradient.addColorStop(0, color.replace(')', ', 0.9)').replace('rgb', 'rgba'));
            gradient.addColorStop(1, color.replace(')', ', 0)').replace('rgb', 'rgba'));
            ctx.fillStyle = gradient;
            ctx.fill();
        }

        // Sparkle effect
        for (let i = 0; i < 10; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const size = Math.random() * 2 + 1;

            ctx.beginPath();
            ctx.moveTo(x, y - size);
            ctx.lineTo(x, y + size);
            ctx.moveTo(x - size, y);
            ctx.lineTo(x + size, y);
            ctx.strokeStyle = color.replace(')', ', 0.7)').replace('rgb', 'rgba');
            ctx.lineWidth = 1;
            ctx.stroke();
        }
    }

    // Rest of the original methods remain unchanged
    drawSoloLevelingBorder(ctx, color, canvas) {
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;

        const segments = 30;
        const borderMargin = 15;

        for (let i = 0; i < segments; i++) {
            const topX1 = borderMargin + (i * (canvas.width - borderMargin * 2) / segments);
            const topX2 = borderMargin + ((i + 1) * (canvas.width - borderMargin * 2) / segments);

            ctx.beginPath();
            ctx.moveTo(topX1, borderMargin);
            ctx.lineTo(topX2, borderMargin);
            if (i % 2 === 0) {
                ctx.lineTo((topX1 + topX2) / 2, borderMargin - 5);
                ctx.closePath();
            }
            ctx.stroke();

            const bottomY = canvas.height - borderMargin;
            ctx.beginPath();
            ctx.moveTo(topX1, bottomY);
            ctx.lineTo(topX2, bottomY);
            if (i % 2 === 0) {
                ctx.lineTo((topX1 + topX2) / 2, bottomY + 5);
                ctx.closePath();
            }
            ctx.stroke();

            if (i < segments / 2) {
                const sideY1 = borderMargin + (i * (canvas.height - borderMargin * 2) / (segments / 2));
                const sideY2 = borderMargin + ((i + 1) * (canvas.height - borderMargin * 2) / (segments / 2));

                ctx.beginPath();
                ctx.moveTo(borderMargin, sideY1);
                ctx.lineTo(borderMargin, sideY2);
                if (i % 2 === 0) {
                    ctx.lineTo(borderMargin - 5, (sideY1 + sideY2) / 2);
                    ctx.closePath();
                }
                ctx.stroke();

                const rightX = canvas.width - borderMargin;
                ctx.beginPath();
                ctx.moveTo(rightX, sideY1);
                ctx.lineTo(rightX, sideY2);
                if (i % 2 === 0) {
                    ctx.lineTo(rightX + 5, (sideY1 + sideY2) / 2);
                    ctx.closePath();
                }
                ctx.stroke();
            }
        }

        const cornerSize = 40;

        ctx.beginPath();
        ctx.moveTo(borderMargin, borderMargin + cornerSize);
        ctx.lineTo(borderMargin, borderMargin);
        ctx.lineTo(borderMargin + cornerSize, borderMargin);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(canvas.width - borderMargin - cornerSize, borderMargin);
        ctx.lineTo(canvas.width - borderMargin, borderMargin);
        ctx.lineTo(canvas.width - borderMargin, borderMargin + cornerSize);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(borderMargin, canvas.height - borderMargin - cornerSize);
        ctx.lineTo(borderMargin, canvas.height - borderMargin);
        ctx.lineTo(borderMargin + cornerSize, canvas.height - borderMargin);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(canvas.width - borderMargin - cornerSize, canvas.height - borderMargin);
        ctx.lineTo(canvas.width - borderMargin, canvas.height - borderMargin);
        ctx.lineTo(canvas.width - borderMargin, canvas.height - borderMargin - cornerSize);
        ctx.stroke();
    }

    drawWorldBossHeader(ctx, x, y, color) {
        ctx.shadowColor = color;
        ctx.shadowBlur = 20;
        ctx.font = 'bold 42px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.textAlign = 'center';
        ctx.fillText("WORLD BOSS ALERT", x, y);

        ctx.beginPath();
        ctx.moveTo(x - 230, y + 10);
        ctx.lineTo(x - 30, y + 10);
        ctx.moveTo(x + 30, y + 10);
        ctx.lineTo(x + 230, y + 10);
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(x - 240, y + 5);
        ctx.lineTo(x - 240, y + 15);
        ctx.lineTo(x - 230, y + 15);
        ctx.moveTo(x + 240, y + 5);
        ctx.lineTo(x + 240, y + 15);
        ctx.lineTo(x + 230, y + 15);
        ctx.stroke();

        ctx.shadowBlur = 0;
    }

    drawStatusWindow(ctx, x, y, width, height, color) {
        ctx.fillStyle = 'rgba(10, 12, 24, 0.7)';
        ctx.fillRect(x, y, width, height);

        ctx.shadowColor = color;
        ctx.shadowBlur = 10;
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, width, height);
        ctx.shadowBlur = 0;

        ctx.beginPath();
        ctx.moveTo(x + 20, y);
        ctx.lineTo(x + 20, y + 20);
        ctx.lineTo(x, y + 20);
        ctx.strokeStyle = color;
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(x + width - 20, y + height);
        ctx.lineTo(x + width - 20, y + height - 20);
        ctx.lineTo(x + width, y + height - 20);
        ctx.stroke();
    }

    drawSLInfoLine(ctx, _icon, title, description, x, y) {
        ctx.font = 'bold 28px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#88aaff';
        ctx.textAlign = 'left';
        ctx.fillText(title + ":", x, y);

        const titleWidth = ctx.measureText(title + ":").width;
        const descriptionX = x + titleWidth + 20;

        ctx.shadowColor = 'rgba(255, 255, 255, 0.5)';
        ctx.shadowBlur = 5;
        ctx.font = '28px "Whitney", Arial, sans-serif';
        ctx.fillStyle = '#ffffff';
        ctx.fillText(description, descriptionX, y);
        ctx.shadowBlur = 0;
    }

    async sendWorldBossAlert(worldBossData) {
        try {
            const enabledServers = await configWrapper.getEnabledWorldBossServers();

            if (enabledServers.length === 0) {
                console.log(chalk.yellow('⚠️ No servers configured for world boss alerts'));
                return;
            }

            const imageBuffer = await this.createWorldBossImage(worldBossData);
            if (!imageBuffer) {
                console.error(chalk.red('❌ Failed to create world boss image'));
                return;
            }

            for (const server of enabledServers) {
                try {
                    const channel = await this.client.channels.fetch(server.config.targetChannelId);
                    if (!channel) {
                        console.error(chalk.red(`❌ Target channel not found for server ${server.name} (${server.serverId})`));
                        continue;
                    }

                    const attachment = new AttachmentBuilder(imageBuffer, { name: 'worldboss_alert.png' });

                    const embed = new EmbedBuilder()
                        .setTitle('🌍 WORLD BOSS ALERT')
                        .setImage('attachment://worldboss_alert.png')
                        .setColor('#aa44ff');

                    // Build ping content using new boss-based structure
                    const worldBossRoles = server.config.worldBossRoles || {};
                    const worldBossPings = [];

                    // Add general world boss ping role if configured AND enabled
                    const generalPingEnabled = server.config.generalPingEnabled !== false; // Default to true for backward compatibility
                    if (generalPingEnabled && worldBossRoles.general) {
                        worldBossPings.push(`<@&${worldBossRoles.general}>`);
                    }

                    // Add legacy roleId if no general role but roleId exists AND general ping is enabled
                    if (generalPingEnabled && !worldBossRoles.general && server.config.roleId) {
                        worldBossPings.push(`<@&${server.config.roleId}>`);
                    }

                    // Add boss-specific ping roles if enabled
                    const bossesPingEnabled = server.config.bossesPingEnabled !== false; // Default to true for backward compatibility
                    if (bossesPingEnabled) {
                        for (const bossInfo of Object.values(worldBossData.worldBosses)) {
                            if (bossInfo && bossInfo.boss) {
                                // Convert boss name to key format (lowercase, spaces to underscores)
                                const bossKey = bossInfo.boss.toLowerCase().replace(/\s+/g, '_');

                                // Check if there's a specific role for this boss
                                if (worldBossRoles[bossKey]) {
                                    worldBossPings.push(`<@&${worldBossRoles[bossKey]}>`);
                                }
                            }
                        }
                    }

                    // Use worldBossPings from the data if available (from worldboss2.js)
                    if (worldBossData.worldBossPings) {
                        for (const pings of Object.values(worldBossData.worldBossPings)) {
                            if (pings && Array.isArray(pings)) {
                                worldBossPings.push(...pings);
                            }
                        }
                    }

                    // Remove duplicates and create final ping content
                    const uniquePings = [...new Set(worldBossPings)];
                    const pingContent = uniquePings.length > 0 ? uniquePings.join(' ') : '';

                    await channel.send({
                        content: pingContent,
                        embeds: [embed],
                        files: [attachment]
                    });

                    console.log(chalk.green(`✅ World boss alert sent to ${server.name} with ${worldBossPings.length} specific pings`));

                } catch (serverError) {
                    console.error(chalk.red(`❌ Error sending world boss alert to ${server.name}:`), serverError.message);
                }
            }

        } catch (error) {
            console.error(chalk.red('❌ Error sending world boss alert:'), error);
        }
    }

    async processWorldBossFile(filePath) {
        try {
            const data = await fs.readFile(filePath, 'utf8');
            const worldBossData = JSON.parse(data);

            console.log(chalk.blue('📊 Processing world boss data:'), worldBossData);
            await this.sendWorldBossAlert(worldBossData);
            await fs.unlink(filePath);
            console.log(chalk.green(`✅ Processed and deleted: ${path.basename(filePath)}`));

        } catch (error) {
            console.error(chalk.red('❌ Error processing world boss file:'), error);
        }
    }

    async watchForWorldBossFiles() {
        try {
            const files = await fs.readdir(this.watchFolder);

            for (const file of files) {
                if (file.endsWith('.json') && file.startsWith('worldboss_') && !this.processedFiles.has(file)) {
                    this.processedFiles.add(file);
                    const filePath = path.join(this.watchFolder, file);

                    console.log(chalk.cyan(`🔍 Found new world boss file: ${file}`));
                    await this.processWorldBossFile(filePath);
                }
            }
        } catch (error) {
            if (error.code !== 'ENOENT') {
                console.error(chalk.red('❌ Error watching for world boss files:'), error);
            }
        }
    }

    async initialize() {
        try {
            console.log(chalk.blue('🌍 Initializing World Boss Alert system...'));
            await this.ensureWatchFolder();

            this.isWatching = true;
            this.watchInterval = setInterval(() => {
                if (this.isWatching) {
                    this.watchForWorldBossFiles();
                }
            }, 2000);

            console.log(chalk.green('✅ World Boss Alert system initialized'));
            console.log(chalk.yellow(`📁 Watching folder: ${this.watchFolder}`));

            const enabledServers = await configWrapper.getEnabledWorldBossServers();
            console.log(chalk.yellow(`🌍 Enabled servers: ${enabledServers.length}`));
            enabledServers.forEach(server => {
                console.log(chalk.yellow(`  - ${server.name}: Channel ${server.config.targetChannelId}, Role ${server.config.roleId}`));
            });

        } catch (error) {
            console.error(chalk.red('❌ Failed to initialize World Boss Alert system:'), error);
        }
    }

    async shutdown() {
        console.log(chalk.yellow('🛑 Shutting down World Boss Alert system...'));

        this.isWatching = false;

        if (this.watchInterval) {
            clearInterval(this.watchInterval);
            this.watchInterval = null;
        }

        console.log(chalk.green('✅ World Boss Alert system shut down successfully'));
    }

    // Draw hexagonal world boss icon
    async drawWorldBossIcon(ctx, x, y, color) {
        // Draw hexagon background
        this.drawHexagon(ctx, x, y, 55, color);

        // Draw world boss skull icon inside hexagon
        this.drawWorldBossSkull(ctx, x, y);
    }

    // Draw hexagon shape (similar to dungeon rank hexagon)
    drawHexagon(ctx, x, y, size, color) {
        ctx.save();

        // Create hexagon path
        ctx.beginPath();
        for (let i = 0; i < 6; i++) {
            const angle = (i * Math.PI / 3);
            const px = x + size * Math.cos(angle);
            const py = y + size * Math.sin(angle);
            if (i === 0) ctx.moveTo(px, py);
            else ctx.lineTo(px, py);
        }
        ctx.closePath();

        // Enhanced gradient fill - more glow around edges, clear center for white image
        const gradientFill = ctx.createRadialGradient(x, y, 0, x, y, size);
        gradientFill.addColorStop(0, 'rgba(170, 68, 255, 0.05)'); // Very subtle center
        gradientFill.addColorStop(0.6, 'rgba(170, 68, 255, 0.15)'); // Gradual increase
        gradientFill.addColorStop(0.8, 'rgba(170, 68, 255, 0.25)'); // More glow near edges
        gradientFill.addColorStop(1, 'rgba(170, 68, 255, 0.1)'); // Soft edge
        ctx.fillStyle = gradientFill;
        ctx.fill();

        // Outer glow effect - multiple layers for better glow
        for (let i = 3; i >= 1; i--) {
            ctx.beginPath();
            for (let j = 0; j < 6; j++) {
                const angle = (j * Math.PI / 3);
                const glowSize = size + (i * 3);
                const px = x + glowSize * Math.cos(angle);
                const py = y + glowSize * Math.sin(angle);
                if (j === 0) ctx.moveTo(px, py);
                else ctx.lineTo(px, py);
            }
            ctx.closePath();

            ctx.strokeStyle = `rgba(170, 68, 255, ${0.3 / i})`;
            ctx.lineWidth = 2;
            ctx.stroke();
        }

        // Main hexagon border with enhanced glow
        ctx.beginPath();
        for (let i = 0; i < 6; i++) {
            const angle = (i * Math.PI / 3);
            const px = x + size * Math.cos(angle);
            const py = y + size * Math.sin(angle);
            if (i === 0) ctx.moveTo(px, py);
            else ctx.lineTo(px, py);
        }
        ctx.closePath();

        // Enhanced shadow/glow for main border
        ctx.shadowColor = color;
        ctx.shadowBlur = 20;
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.stroke();

        // Add inner highlight for more depth
        ctx.shadowBlur = 0;
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 1;
        ctx.stroke();

        ctx.restore();
    }

    // Draw world boss skull icon inside hexagon using actual image file
    async drawWorldBossSkull(ctx, x, y) {
        try {
            // Load the skull image
            const imagePath = path.join(__dirname, '../images/worldboss.png');
            const skullImage = await loadImage(imagePath);

            // Calculate size to fit within hexagon (slightly smaller than hexagon radius)
            const imageSize = 80; // Fits well within the 55px hexagon radius
            const drawX = x - imageSize / 2;
            const drawY = y - imageSize / 2;

            // Save context for any effects
            ctx.save();

            // Add subtle glow effect around the image
            ctx.shadowColor = '#aa44ff';
            ctx.shadowBlur = 10;

            // Draw the skull image centered in the hexagon
            ctx.drawImage(skullImage, drawX, drawY, imageSize, imageSize);

            // Reset shadow
            ctx.shadowBlur = 0;

            // Restore context
            ctx.restore();

        } catch (error) {
            console.error(chalk.yellow('⚠️ Could not load world boss image, falling back to drawn skull:'), error.message);

            // Fallback to a simple drawn skull if image loading fails
            this.drawFallbackSkull(ctx, x, y);
        }
    }

    // Fallback skull drawing method (simplified version)
    drawFallbackSkull(ctx, x, y) {
        const scale = 0.8;
        const iconSize = 45 * scale;

        ctx.save();

        // Simple skull shape
        ctx.fillStyle = '#ff3333';
        ctx.strokeStyle = '#ff3333';
        ctx.lineWidth = 2;

        const skullSize = iconSize * 0.7;

        // Main skull shape
        ctx.fillRect(x - skullSize * 0.3, y - skullSize * 0.4, skullSize * 0.6, skullSize * 0.6);

        // Eye sockets
        ctx.fillStyle = '#000000';
        const eyeSize = skullSize * 0.12;
        const eyeOffset = skullSize * 0.18;

        ctx.fillRect(x - eyeOffset - eyeSize / 2, y - skullSize * 0.15 - eyeSize / 2, eyeSize, eyeSize);
        ctx.fillRect(x + eyeOffset - eyeSize / 2, y - skullSize * 0.15 - eyeSize / 2, eyeSize, eyeSize);

        // Nasal cavity
        ctx.beginPath();
        ctx.moveTo(x, y - skullSize * 0.05);
        ctx.lineTo(x - skullSize * 0.08, y + skullSize * 0.05);
        ctx.lineTo(x, y + skullSize * 0.15);
        ctx.lineTo(x + skullSize * 0.08, y + skullSize * 0.05);
        ctx.closePath();
        ctx.fill();

        ctx.restore();
    }

    async getStatus() {
        const enabledServers = await configWrapper.getEnabledWorldBossServers();
        return {
            isWatching: this.isWatching,
            watchFolder: this.watchFolder,
            enabledServers: enabledServers.length,
            servers: enabledServers.map(s => ({ name: s.name, serverId: s.serverId })),
            processedFiles: this.processedFiles.size
        };
    }
}

module.exports = WorldBossAlert;