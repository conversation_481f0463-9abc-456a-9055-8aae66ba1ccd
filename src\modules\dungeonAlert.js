const { EmbedBuilder, AttachmentBuilder } = require('discord.js');
const fs = require('fs');
const path = require('path');
const chokidar = require('chokidar');
const chalk = require('chalk');
const { createCanvas } = require('canvas');
// serverConfigs will be loaded dynamically to ensure fresh configuration

class DungeonAlert {
  constructor(client) {
    this.client = client;
    this.sharedFolder = path.join(__dirname, '../../data/alerts/dungeon');

    // Create shared folder if it doesn't exist
    if (!fs.existsSync(this.sharedFolder)) {
      fs.mkdirSync(this.sharedFolder, { recursive: true });
      //console.log(chalk.green(`Created dungeon alerts folder: ${this.sharedFolder}`));
    }

    // Shared configuration will be loaded dynamically when needed

    // Store recent dungeon messages for room updates (server ID -> array of message data)
    this.recentDungeonMessages = new Map();

    // Server ID that supports rooms feature
    this.ROOMS_ENABLED_SERVER = '923445406849990728';

    // Track the current dungeon cycle to group messages
    this.currentDungeonCycle = null;
  }

  // Helper method to get fresh shared configuration
  async getFreshSharedConfig() {
    // Use the new configuration wrapper for database/file compatibility
    const configWrapper = require('../services/configWrapper');
    return await configWrapper.getSharedConfig();
  }

  initialize() {
    console.log(`[DungeonAlert] Initializing watcher for folder: ${this.sharedFolder}`);

    // Watch the shared folder for new files
    const watcher = chokidar.watch(this.sharedFolder, {
      ignored: /(^|[\/\\])\../,
      persistent: true
    });

    watcher.on('add', (filePath) => {
      //console.log(`[DungeonAlert] New file detected: ${filePath}`);
      this.processNewFile(filePath);
    });

    watcher.on('error', (error) => {
      console.error('[DungeonAlert] Watcher error:', error);
    });

    console.log('[DungeonAlert] Dungeon Alert system initialized');
  }

  async processNewFile(filePath) {
    try {
      //console.log(`[DungeonAlert] Reading file: ${filePath}`);

      // Check if file should be processed based on time windows
      if (!this.shouldProcessFile()) {
        //console.log(`[DungeonAlert] File outside time window, deleting: ${filePath}`);
        fs.unlinkSync(filePath);
        return;
      }

      const fileData = fs.readFileSync(filePath, 'utf8');
      const data = JSON.parse(fileData);

      //console.log(`[DungeonAlert] Processing data:`, JSON.stringify({
      //  title: data.title,
      //  type: data.type,
      //  messageId: data.messageId
      //}));

      if (data.type === 'user_message') {
        await this.processUserDungeonMessage(data);
      } else {
        // Default to embed processing for backward compatibility
        await this.processDungeonEmbed(data);
      }

      //console.log(`[DungeonAlert] Deleting processed file: ${filePath}`);
      fs.unlinkSync(filePath);
    } catch (error) {
      console.error(chalk.red('[DungeonAlert] Error processing file:'), error);
    }
  }

  shouldProcessFile() {
    // Get current time in Asia/Kolkata timezone
    const now = new Date();
    const kolkataTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));

    const minutes = kolkataTime.getMinutes();

    // Target times: 00:15, 00:30, 00:45, 01:00 (every 15th minute)
    // Allow 1-minute buffer before and after each target time
    const targetMinutes = [15, 30, 45, 0]; // 0 represents 60 minutes (01:00)

    for (const targetMinute of targetMinutes) {
      const adjustedTarget = targetMinute === 0 ? 60 : targetMinute;

      // Check if current time is within 1-minute buffer of target time
      // For example: 14-16 minutes for 15-minute target
      const lowerBound = adjustedTarget - 1;
      const upperBound = adjustedTarget + 1;

      // Handle edge case for 60 minutes (01:00)
      if (targetMinute === 0) {
        // Check for 59-01 minute range (59, 0, 1)
        if (minutes >= 59 || minutes <= 1) {
          return true;
        }
      } else {
        // Normal range check
        if (minutes >= lowerBound && minutes <= upperBound) {
          return true;
        }
      }
    }

    // File is outside all time windows, should be deleted
    return false;
  }

  async processUserDungeonMessage(userData) {
    try {
      //console.log('[DungeonAlert] Processing user dungeon message');

      // Get fresh shared configuration
      const sharedConfig = await this.getFreshSharedConfig();
      const islandBosses = sharedConfig.islandBosses;

      // Create dungeon info from user data using island-specific boss and stored details from last embed
      const dungeonInfo = {
        world: userData.world,
        island: userData.island,
        originalMessage: userData.originalMessage,
        // Use island-specific boss, but other details from the last dungeon embed
        boss: userData.boss || islandBosses[userData.island] || 'Unknown',
        rank: userData.rank || 'S',
        isRedDungeon: userData.isRedDungeon !== undefined ? userData.isRedDungeon : true,
        isDoubleDungeon: userData.isDoubleDungeon !== undefined ? userData.isDoubleDungeon : true
      };

      // Send to all enabled servers
      await this.sendDungeonAlertToServers(dungeonInfo, userData.title);

    } catch (error) {
      console.error('[DungeonAlert] Error processing user dungeon message:', error);
      throw error;
    }
  }

  async processDungeonEmbed(embedData) {
    try {
      //console.log('[DungeonAlert] Parsing dungeon info from description');
      const dungeonInfo = await this.parseDungeonInfo(embedData.description);

      if (!dungeonInfo) {
        //console.log('[DungeonAlert] Failed to parse dungeon info - skipping');
        return;
      }

      // Add world information to dungeon info
      dungeonInfo.world = await this.getWorldFromIsland(dungeonInfo.island);

      //console.log('[DungeonAlert] Parsed dungeon info:', dungeonInfo);

      // Send to all enabled servers
      await this.sendDungeonAlertToServers(dungeonInfo, embedData.title);

    } catch (error) {
      console.error('[Dungeon Alert] Error processing Dungeon embed:', error);
      throw error;
    }
  }

  async sendDungeonAlertToServers(dungeonInfo, title) {
    try {
      // Use the new configuration wrapper for database/file compatibility
      const configWrapper = require('../services/configWrapper');

      // Get all enabled servers for dungeon alerts
      const enabledServers = await configWrapper.getEnabledDungeonServers();

      //console.log(chalk.blue(`[DungeonAlert] Found ${enabledServers.length} enabled servers for dungeon alerts`));
      enabledServers.forEach(server => {
        //console.log(chalk.blue(`  - ${server.name} (${server.serverId}) -> Channel: ${server.config.targetChannelId}`));
      });

      if (enabledServers.length === 0) {
        console.log(chalk.yellow('⚠️ No servers configured for dungeon alerts'));
        return;
      }

      // Create the image once
      const attachment = await this.createDungeonImage(dungeonInfo);
      const rankColor = await this.getRankColor(dungeonInfo.rank);

      // Send to all enabled servers simultaneously
      const sendPromises = enabledServers.map(async (server) => {
        try {
          const serverConfig = server.config;
          const channel = await this.client.channels.fetch(serverConfig.targetChannelId);

          if (!channel) {
            console.error(chalk.red(`❌ Target channel not found for server ${server.name} (${server.serverId})`));
            return;
          }

          // Build role pings for this server
          const rolesToPing = [];

          // Add general dungeon ping role (always enabled if configured)
          if (serverConfig.dungeonRoles.DUNGEON_PING) {
            rolesToPing.push(`<@&${serverConfig.dungeonRoles.DUNGEON_PING}>`);
          }

          // Add world ping role (check if world roles are enabled)
          if (dungeonInfo.world && serverConfig.worldRoles[dungeonInfo.world] && serverConfig.worldRolesEnabled !== false) {
            rolesToPing.push(`<@&${serverConfig.worldRoles[dungeonInfo.world]}>`);
          }

          // Add rank role (check if rank roles are enabled)
          if (dungeonInfo.rank && serverConfig.dungeonRoles[dungeonInfo.rank] && serverConfig.rankRolesEnabled !== false) {
            rolesToPing.push(`<@&${serverConfig.dungeonRoles[dungeonInfo.rank]}>`);
          }

          // Add island role (check if island roles are enabled)
          if (dungeonInfo.island && serverConfig.islandRoles[dungeonInfo.island] && serverConfig.islandRolesEnabled !== false) {
            rolesToPing.push(`<@&${serverConfig.islandRoles[dungeonInfo.island]}>`);
          }

          // Add special dungeon roles (check if special roles are enabled)
          if (dungeonInfo.isRedDungeon && serverConfig.dungeonRoles.RED_DUNGEON && serverConfig.specialRolesEnabled !== false) {
            rolesToPing.push(`<@&${serverConfig.dungeonRoles.RED_DUNGEON}>`);
          }

          if (dungeonInfo.isDoubleDungeon && serverConfig.dungeonRoles.DOUBLE_DUNGEON && serverConfig.specialRolesEnabled !== false) {
            rolesToPing.push(`<@&${serverConfig.dungeonRoles.DOUBLE_DUNGEON}>`);
          }

          // Create embed for this server
          const newEmbed = new EmbedBuilder()
            .setTitle(title)
            .setColor(rankColor)
            .setImage('attachment://dungeon-info.png')
            .setFooter({ text: 'Beta Access | Public Soon!' });

          // Create new attachment for this server (Discord requires separate instances)
          const serverAttachment = new AttachmentBuilder(attachment.attachment, { name: 'dungeon-info.png' });

          // Send the message
          const sentMessage = await channel.send({
            content: rolesToPing.join(' '),
            embeds: [newEmbed],
            files: [serverAttachment]
          });

          // Store message info for rooms feature (only for enabled server)
          if (server.serverId === this.ROOMS_ENABLED_SERVER) {
            // Get current dungeon cycle (based on time)
            const currentCycle = this.getCurrentDungeonCycle();

            // Initialize array for this server if it doesn't exist
            if (!this.recentDungeonMessages.has(server.serverId)) {
              this.recentDungeonMessages.set(server.serverId, []);
            }

            const serverMessages = this.recentDungeonMessages.get(server.serverId);

            // Add new message to the array
            serverMessages.push({
              messageId: sentMessage.id,
              channelId: channel.id,
              dungeonInfo: dungeonInfo,
              title: title,
              timestamp: Date.now(),
              cycle: currentCycle
            });

            console.log(`[DungeonAlert] 📝 Stored dungeon message ${sentMessage.id} (cycle: ${currentCycle})`);

            // Clean up old entries
            this.cleanupOldDungeonMessages();
          }

          //console.log(chalk.green(`✅ Dungeon alert sent to ${server.name}`));

        } catch (serverError) {
          //console.error(chalk.red(`❌ Error sending dungeon alert to ${server.name}:`), serverError.message);
        }
      });

      // Wait for all messages to be sent simultaneously
      await Promise.all(sendPromises);

    } catch (error) {
      console.error(chalk.red('❌ Error sending dungeon alerts:'), error);
      throw error;
    }
  }

  async parseDungeonInfo(description) {
    if (!description) return null;

    const yamlMatch = description.match(/```(?:yaml)?\s*([\s\S]*?)```/);

    if (!yamlMatch) {
      //console.log('Could not find YAML content in description');
      return null;
    }

    const yamlContent = yamlMatch[1].trim();
    const infoLines = yamlContent.split('\n').filter(line => line.trim() !== '');

    let islandRaw = this.extractValue(infoLines.find(line => line.includes('Island')));
    let island = await this.normalizeIslandName(islandRaw);

    const dungeonInfo = {
      island: island,
      originalIsland: islandRaw,
      map: this.extractValue(infoLines.find(line => line.includes('Map'))),
      boss: this.extractValue(infoLines.find(line => line.includes('Boss'))),
      rank: this.extractValue(infoLines.find(line => line.includes('Rank'))),
      isRedDungeon: this.checkForTick(infoLines.find(line => line.includes('Red Dungeon'))),
      isDoubleDungeon: this.checkForTick(infoLines.find(line => line.includes('Double Dungeon')))
    };

    return dungeonInfo;
  }

  async getWorldFromIsland(islandName) {
    if (!islandName) return null;

    // Get fresh shared configuration
    const sharedConfig = await this.getFreshSharedConfig();
    const worldIslands = sharedConfig.worldIslands;

    for (const [world, islands] of Object.entries(worldIslands)) {
      if (islands.includes(islandName)) {
        return parseInt(world);
      }
    }
    return null;
  }

  async normalizeIslandName(islandName) {
    if (!islandName) return '';

    const normalized = islandName.trim();

    // Get fresh shared configuration
    const sharedConfig = await this.getFreshSharedConfig();
    const worldIslands = sharedConfig.worldIslands;

    // Check if worldIslands is available
    if (!worldIslands) {
      console.error('[DungeonAlert] worldIslands is undefined! Falling back to normalized name.');
      return normalized;
    }

    // Get all known islands from the shared world islands configuration
    const allIslands = [];
    for (const islands of Object.values(worldIslands)) {
      allIslands.push(...islands);
    }

    // Direct match
    for (const island of allIslands) {
      if (island.toLowerCase() === normalized.toLowerCase()) {
        return island;
      }
    }

    // Partial match
    for (const island of allIslands) {
      if (normalized.toLowerCase().includes(island.toLowerCase()) ||
        island.toLowerCase().includes(normalized.toLowerCase())) {
        return island;
      }
    }

    return normalized;
  }

  extractValue(line) {
    if (!line) return '';

    const match = line.match(/:\s*(.+)$/);
    if (match) {
      let value = match[1].trim();
      value = value.replace(/[✅❌]\s*(Yes|No)$/, '').trim();
      return value;
    }
    return '';
  }

  checkForTick(line) {
    if (!line) return false;
    return line.includes('✅') || line.includes('Yes');
  }

  async createDungeonImage(dungeonInfo) {
    const canvas = createCanvas(800, 500);
    const ctx = canvas.getContext('2d');

    const rankColor = await this.getRankColor(dungeonInfo.rank);

    // Fill background with dark gradient
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#0f1016');
    gradient.addColorStop(1, '#1a1b26');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add magical particles effect
    this.drawMagicalParticles(ctx, rankColor, canvas);

    // Draw stylized border
    this.drawSoloLevelingBorder(ctx, rankColor, canvas);

    // Draw header
    this.drawGateHeader(ctx, canvas.width / 2, 60, rankColor);

    // Draw rank
    await this.drawSoloLevelingRank(ctx, dungeonInfo.rank, canvas.width / 2, 140);

    // Draw status window panel
    const panelX = 40;
    const panelY = 200;
    const panelWidth = canvas.width - 80;
    const panelHeight = 240;
    this.drawStatusWindow(ctx, panelX, panelY, panelWidth, panelHeight, rankColor);

    // Draw dungeon info
    let y = 240;
    const leftMargin = 70;
    const lineSpacing = 60;

    // Show World instead of Island, and Island instead of Map
    this.drawSLInfoLine(ctx, '', 'World', dungeonInfo.world ? `World ${dungeonInfo.world}` : 'Unknown', leftMargin, y);
    y += lineSpacing;

    this.drawSLInfoLine(ctx, '', 'Island', dungeonInfo.island || 'Unknown', leftMargin, y);
    y += lineSpacing;

    // Draw Boss and Rooms on the same line
    this.drawSLInfoLine(ctx, '', 'Boss', dungeonInfo.boss || 'Unknown', leftMargin, y);

    // Add rooms information to the right of Boss (if available)
    if (dungeonInfo.rooms) {
      const halfWidth = canvas.width / 2;
      this.drawSLInfoLine(ctx, '', 'Rooms', dungeonInfo.rooms.toString(), halfWidth + 20, y);
    }

    y += lineSpacing;

    // Special indicators
    const specialY = y;
    const halfWidth = canvas.width / 2;

    this.drawSLStatusIndicator(ctx, '', 'Red Gate', dungeonInfo.isRedDungeon, leftMargin, specialY);
    this.drawSLStatusIndicator(ctx, '', 'Double Dungeon', dungeonInfo.isDoubleDungeon, halfWidth - 30, specialY);

    // Add watermark
    ctx.font = 'bold 18px "Whitney", Arial, sans-serif';
    ctx.fillStyle = rankColor.replace(')', ', 0.8)').replace('rgb', 'rgba');
    ctx.textAlign = 'center';
    ctx.fillText("RankBreaker | discord.gg/M6e3PGRtd3", canvas.width / 2, canvas.height - 20);

    const buffer = canvas.toBuffer();
    return new AttachmentBuilder(buffer, { name: 'dungeon-info.png' });
  }

  drawMagicalParticles(ctx, color, canvas) {
    const particleCount = 100;

    for (let i = 0; i < particleCount; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const size = Math.random() * 2 + 1;
      const alpha = Math.random() * 0.5 + 0.1;

      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);
      ctx.fillStyle = color.replace(')', `, ${alpha})`).replace('rgb', 'rgba');
      ctx.fill();
    }

    for (let i = 0; i < 20; i++) {
      const x = Math.random() * canvas.width;
      const y = Math.random() * canvas.height;
      const size = Math.random() * 4 + 2;

      ctx.beginPath();
      ctx.arc(x, y, size, 0, Math.PI * 2);

      const gradient = ctx.createRadialGradient(x, y, 0, x, y, size * 2);
      gradient.addColorStop(0, color.replace(')', ', 0.8)').replace('rgb', 'rgba'));
      gradient.addColorStop(1, color.replace(')', ', 0)').replace('rgb', 'rgba'));

      ctx.fillStyle = gradient;
      ctx.fill();
    }
  }

  drawSoloLevelingBorder(ctx, color, canvas) {
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;

    const segments = 30;
    const borderMargin = 15;

    for (let i = 0; i < segments; i++) {
      const topX1 = borderMargin + (i * (canvas.width - borderMargin * 2) / segments);
      const topX2 = borderMargin + ((i + 1) * (canvas.width - borderMargin * 2) / segments);

      ctx.beginPath();
      ctx.moveTo(topX1, borderMargin);
      ctx.lineTo(topX2, borderMargin);
      if (i % 2 === 0) {
        ctx.lineTo((topX1 + topX2) / 2, borderMargin - 5);
        ctx.closePath();
      }
      ctx.stroke();

      const bottomY = canvas.height - borderMargin;
      ctx.beginPath();
      ctx.moveTo(topX1, bottomY);
      ctx.lineTo(topX2, bottomY);
      if (i % 2 === 0) {
        ctx.lineTo((topX1 + topX2) / 2, bottomY + 5);
        ctx.closePath();
      }
      ctx.stroke();

      if (i < segments / 2) {
        const sideY1 = borderMargin + (i * (canvas.height - borderMargin * 2) / (segments / 2));
        const sideY2 = borderMargin + ((i + 1) * (canvas.height - borderMargin * 2) / (segments / 2));

        ctx.beginPath();
        ctx.moveTo(borderMargin, sideY1);
        ctx.lineTo(borderMargin, sideY2);
        if (i % 2 === 0) {
          ctx.lineTo(borderMargin - 5, (sideY1 + sideY2) / 2);
          ctx.closePath();
        }
        ctx.stroke();

        const rightX = canvas.width - borderMargin;
        ctx.beginPath();
        ctx.moveTo(rightX, sideY1);
        ctx.lineTo(rightX, sideY2);
        if (i % 2 === 0) {
          ctx.lineTo(rightX + 5, (sideY1 + sideY2) / 2);
          ctx.closePath();
        }
        ctx.stroke();
      }
    }

    const cornerSize = 40;

    ctx.beginPath();
    ctx.moveTo(borderMargin, borderMargin + cornerSize);
    ctx.lineTo(borderMargin, borderMargin);
    ctx.lineTo(borderMargin + cornerSize, borderMargin);
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(canvas.width - borderMargin - cornerSize, borderMargin);
    ctx.lineTo(canvas.width - borderMargin, borderMargin);
    ctx.lineTo(canvas.width - borderMargin, borderMargin + cornerSize);
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(borderMargin, canvas.height - borderMargin - cornerSize);
    ctx.lineTo(borderMargin, canvas.height - borderMargin);
    ctx.lineTo(borderMargin + cornerSize, canvas.height - borderMargin);
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(canvas.width - borderMargin - cornerSize, canvas.height - borderMargin);
    ctx.lineTo(canvas.width - borderMargin, canvas.height - borderMargin);
    ctx.lineTo(canvas.width - borderMargin, canvas.height - borderMargin - cornerSize);
    ctx.stroke();
  }

  drawGateHeader(ctx, x, y, color) {
    ctx.shadowColor = color;
    ctx.shadowBlur = 20;
    ctx.font = 'bold 42px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'center';
    ctx.fillText("DUNGEON ALERT", x, y);

    ctx.beginPath();
    ctx.moveTo(x - 230, y + 10);
    ctx.lineTo(x - 30, y + 10);
    ctx.moveTo(x + 30, y + 10);
    ctx.lineTo(x + 230, y + 10);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(x - 240, y + 5);
    ctx.lineTo(x - 240, y + 15);
    ctx.lineTo(x - 230, y + 15);
    ctx.moveTo(x + 240, y + 5);
    ctx.lineTo(x + 240, y + 15);
    ctx.lineTo(x + 230, y + 15);
    ctx.stroke();

    ctx.shadowBlur = 0;
  }

  async drawSoloLevelingRank(ctx, rank, x, y) {
    const rankColor = await this.getRankColor(rank);

    this.drawHexagon(ctx, x, y, 55, rankColor);

    ctx.shadowColor = rankColor;
    ctx.shadowBlur = 15;
    ctx.font = 'bold 70px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(rank, x, y);
    ctx.shadowBlur = 0;
    ctx.textBaseline = 'alphabetic';
  }

  drawHexagon(ctx, x, y, size, color) {
    ctx.beginPath();
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI / 3);
      const px = x + size * Math.cos(angle);
      const py = y + size * Math.sin(angle);
      if (i === 0) ctx.moveTo(px, py);
      else ctx.lineTo(px, py);
    }
    ctx.closePath();

    ctx.shadowColor = color;
    ctx.shadowBlur = 15;
    ctx.strokeStyle = color;
    ctx.lineWidth = 3;
    ctx.stroke();

    const gradientFill = ctx.createRadialGradient(x, y, 0, x, y, size);
    gradientFill.addColorStop(0, color.replace(')', ', 0.2)').replace('rgb', 'rgba'));
    gradientFill.addColorStop(1, color.replace(')', ', 0.05)').replace('rgb', 'rgba'));
    ctx.fillStyle = gradientFill;
    ctx.fill();
    ctx.shadowBlur = 0;
  }

  drawStatusWindow(ctx, x, y, width, height, color) {
    ctx.fillStyle = 'rgba(10, 12, 24, 0.7)';
    ctx.fillRect(x, y, width, height);

    ctx.shadowColor = color;
    ctx.shadowBlur = 10;
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.strokeRect(x, y, width, height);
    ctx.shadowBlur = 0;

    ctx.beginPath();
    ctx.moveTo(x + 20, y);
    ctx.lineTo(x + 20, y + 20);
    ctx.lineTo(x, y + 20);
    ctx.strokeStyle = color;
    ctx.stroke();

    ctx.beginPath();
    ctx.moveTo(x + width - 20, y + height);
    ctx.lineTo(x + width - 20, y + height - 20);
    ctx.lineTo(x + width, y + height - 20);
    ctx.stroke();
  }

  drawSLInfoLine(ctx, _icon, title, description, x, y) {
    ctx.font = 'bold 28px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#88aaff';
    ctx.textAlign = 'left';
    ctx.fillText(title + ":", x, y);

    const titleWidth = ctx.measureText(title + ":").width;
    const descriptionX = x + titleWidth + 20;

    ctx.shadowColor = 'rgba(255, 255, 255, 0.5)';
    ctx.shadowBlur = 5;
    ctx.font = '28px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.fillText(description, descriptionX, y);
    ctx.shadowBlur = 0;
  }

  drawSLStatusIndicator(ctx, _icon, title, isActive, x, y) {
    ctx.font = 'bold 28px "Whitney", Arial, sans-serif';
    ctx.fillStyle = '#88aaff';
    ctx.textAlign = 'left';
    const titleText = title + ":";
    ctx.fillText(titleText, x, y);

    const titleWidth = ctx.measureText(titleText).width;
    const statusX = x + titleWidth + 20;
    ctx.font = '28px "Whitney", Arial, sans-serif';

    if (isActive) {
      ctx.shadowColor = '#4488ff';
      ctx.shadowBlur = 10;
      ctx.fillStyle = '#4488ff';
      ctx.fillText("✔ Yes", statusX, y);
    } else {
      ctx.shadowColor = '#ff4455';
      ctx.shadowBlur = 10;
      ctx.fillStyle = '#ff4455';
      ctx.fillText("✘ No", statusX, y);
    }
    ctx.shadowBlur = 0;
  }

  async getRankColor(rank) {
    // Get fresh shared configuration
    const sharedConfig = await this.getFreshSharedConfig();
    return sharedConfig.rankColors[rank] || '#4488ff';
  }

  // Clean up old dungeon messages (updated for array storage)
  cleanupOldDungeonMessages() {
    const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);

    for (const [serverId, messageArray] of this.recentDungeonMessages.entries()) {
      if (Array.isArray(messageArray)) {
        // Filter out old messages
        const recentMessages = messageArray.filter(msg => msg.timestamp >= thirtyMinutesAgo);

        if (recentMessages.length === 0) {
          this.recentDungeonMessages.delete(serverId);
          console.log(`[DungeonAlert] Cleaned up all old messages for server ${serverId}`);
        } else if (recentMessages.length < messageArray.length) {
          this.recentDungeonMessages.set(serverId, recentMessages);
          console.log(`[DungeonAlert] Cleaned up ${messageArray.length - recentMessages.length} old messages for server ${serverId}`);
        }
      } else {
        // Handle old single message format
        if (messageArray.timestamp < thirtyMinutesAgo) {
          this.recentDungeonMessages.delete(serverId);
          console.log(`[DungeonAlert] Cleaned up old dungeon message for server ${serverId}`);
        }
      }
    }
  }

  // Updated debug method for array storage
  getStoredDungeonMessages() {
    console.log(`[DungeonAlert] 📊 Currently stored dungeon messages:`);
    if (this.recentDungeonMessages.size === 0) {
      console.log(`[DungeonAlert] 📭 No stored messages`);
      return;
    }

    for (const [serverId, messageData] of this.recentDungeonMessages.entries()) {
      if (Array.isArray(messageData)) {
        console.log(`[DungeonAlert] 📝 Server ${serverId}: ${messageData.length} messages`);

        messageData.forEach((msg, index) => {
          const ageMinutes = Math.floor((Date.now() - msg.timestamp) / (1000 * 60));
          console.log(`[DungeonAlert]   ${index + 1}. Message ID: ${msg.messageId}`);
          console.log(`[DungeonAlert]      Channel: ${msg.channelId}`);
          console.log(`[DungeonAlert]      Age: ${ageMinutes} minutes`);
          console.log(`[DungeonAlert]      Cycle: ${msg.cycle}`);
          console.log(`[DungeonAlert]      Title: ${msg.title}`);
        });
      } else {
        // Handle old single message format
        const ageMinutes = Math.floor((Date.now() - messageData.timestamp) / (1000 * 60));
        console.log(`[DungeonAlert] 📝 Server ${serverId}: 1 message (old format)`);
        console.log(`[DungeonAlert]   - Message ID: ${messageData.messageId}`);
        console.log(`[DungeonAlert]   - Channel ID: ${messageData.channelId}`);
        console.log(`[DungeonAlert]   - Age: ${ageMinutes} minutes`);
        console.log(`[DungeonAlert]   - Title: ${messageData.title}`);
      }
    }
  }

  // Method to be called from message event handler to check for room updates
  async handlePotentialRoomMessage(message) {
    try {
      // Only process for the enabled server
      if (message.guild?.id !== this.ROOMS_ENABLED_SERVER) {
        return;
      }

      console.log(`[DungeonAlert] 🔍 Checking message in server ${message.guild.id}, channel ${message.channel.id}: "${message.content}"`);

      // Always show stored messages for debugging
      console.log(`[DungeonAlert] 📊 Current stored messages:`);
      this.getStoredDungeonMessages();

      // Extract room count from message first (quick check)
      const roomCount = this.extractRoomCount(message.content);
      if (roomCount === null) {
        console.log(`[DungeonAlert] ❌ No room count found in message: "${message.content}"`);
        return; // Not a room message, exit silently
      }

      console.log(`[DungeonAlert] 🎯 Room message detected: "${message.content}" -> ${roomCount} rooms`);

      // Check if we have recent dungeon messages for this server
      const serverMessages = this.recentDungeonMessages.get(message.guild.id);
      if (!serverMessages) {
        console.log(`[DungeonAlert] ❌ No recent dungeon messages found for server ${message.guild.id}`);
        return;
      }

      // Handle both single message (old format) and array (new format)
      const messagesToUpdate = Array.isArray(serverMessages) ? serverMessages : [serverMessages];
      console.log(`[DungeonAlert] 📝 Found ${messagesToUpdate.length} stored dungeon messages to check`);

      // Get current dungeon cycle
      const currentCycle = this.getCurrentDungeonCycle();
      console.log(`[DungeonAlert] ⏰ Current cycle: ${currentCycle}`);

      // Filter messages from the same channel and from the current dungeon cycle
      // Room messages should come AFTER the dungeon cycle time
      const relevantMessages = messagesToUpdate.filter(msg => {
        console.log(`[DungeonAlert] 🔍 Checking dungeon message: ${msg.messageId} in channel ${msg.channelId}, cycle ${msg.cycle}`);

        // Check if message is from same channel
        if (msg.channelId !== message.channel.id) {
          console.log(`[DungeonAlert] ❌ Channel mismatch: ${msg.channelId} vs ${message.channel.id}`);
          return false;
        }

        // Check if message is from current or recent dungeon cycle
        if (!this.isRecentCycle(msg.cycle, currentCycle)) {
          console.log(`[DungeonAlert] ❌ Cycle not recent: ${msg.cycle} vs ${currentCycle}`);
          return false;
        }

        // Check if room message timestamp is AFTER dungeon message timestamp
        // This ensures we only update dungeons with rooms posted after them
        const isAfter = message.createdTimestamp > msg.timestamp;
        console.log(`[DungeonAlert] ⏰ Timestamp check: room ${message.createdTimestamp} > dungeon ${msg.timestamp} = ${isAfter}`);
        return isAfter;
      });

      if (relevantMessages.length === 0) {
        console.log(`[DungeonAlert] ❌ No relevant dungeon messages found in channel ${message.channel.id} for current cycle ${currentCycle}`);
        console.log(`[DungeonAlert] 📊 Debug info:`);
        console.log(`[DungeonAlert]   - Total stored messages: ${messagesToUpdate.length}`);
        console.log(`[DungeonAlert]   - Room message channel: ${message.channel.id}`);
        console.log(`[DungeonAlert]   - Room message timestamp: ${message.createdTimestamp}`);
        console.log(`[DungeonAlert]   - Current cycle: ${currentCycle}`);
        return;
      }

      console.log(`[DungeonAlert] ✅ Found ${relevantMessages.length} dungeon messages to update with ${roomCount} rooms (cycle: ${currentCycle})`);

      // Update ALL relevant dungeon messages with room information
      for (const dungeonData of relevantMessages) {
        try {
          await this.updateDungeonMessageWithRooms(dungeonData, roomCount);
        } catch (error) {
          console.error(`[DungeonAlert] Failed to update message ${dungeonData.messageId}:`, error);
        }
      }

      console.log(`[DungeonAlert] 🎉 Updated ${relevantMessages.length} dungeon messages with rooms!`);

    } catch (error) {
      console.error('[DungeonAlert] Error handling potential room message:', error);
    }
  }

  // Check if we're in a time window where room messages are expected
  // Room messages typically come 1-10 minutes after dungeon cycle times
  isRoomMessageTimeWindow() {
    const now = new Date();
    const kolkataTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
    const minutes = kolkataTime.getMinutes();

    // Room messages are expected 1-10 minutes after each dungeon cycle
    // Dungeon cycles: xx:00, xx:15, xx:30, xx:45
    // Room windows: xx:01-xx:10, xx:16-xx:25, xx:31-xx:40, xx:46-xx:55
    const roomWindows = [
      { start: 1, end: 10 },   // After xx:00 cycle
      { start: 16, end: 25 },  // After xx:15 cycle
      { start: 31, end: 40 },  // After xx:30 cycle
      { start: 46, end: 55 }   // After xx:45 cycle
    ];

    for (const window of roomWindows) {
      if (minutes >= window.start && minutes <= window.end) {
        return true;
      }
    }

    return false;
  }

  // Extract room count from message content
  extractRoomCount(content) {
    if (!content) return null;

    // Look for patterns like "# 6 ROOMS #", "# 8 ROOMS", "8 rooms", etc.
    const patterns = [
      /#\s*(\d+)\s*rooms?\s*#/i,  // # 6 ROOMS #
      /#\s*(\d+)\s*rooms?/i,      // # 8 ROOMS
      /(\d+)\s*rooms?\s*#/i,      // 6 ROOMS #
      /(?:#\s*)?(\d+)\s*rooms?/i, // 8 ROOMS or # 8 ROOMS
      /rooms?\s*:?\s*(\d+)/i,     // rooms: 4
      /(\d+)\s*room/i             // 4 room
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        const roomCount = parseInt(match[1]);
        if (roomCount > 0 && roomCount <= 20) { // Reasonable room count range
          console.log(`[DungeonAlert] Extracted room count: ${roomCount} from message: "${content}"`);
          return roomCount;
        }
      }
    }

    console.log(`[DungeonAlert] No room count found in message: "${content}"`);
    return null;
  }

  // Update the dungeon message with room information
  async updateDungeonMessageWithRooms(dungeonData, roomCount) {
    try {
      const channel = await this.client.channels.fetch(dungeonData.channelId);
      if (!channel) {
        console.error('[DungeonAlert] Channel not found for room update');
        return;
      }

      const message = await channel.messages.fetch(dungeonData.messageId);
      if (!message) {
        console.error('[DungeonAlert] Message not found for room update');
        return;
      }

      // Create updated dungeon info with room count
      const updatedDungeonInfo = {
        ...dungeonData.dungeonInfo,
        rooms: roomCount
      };

      // Create new image with room information
      const newAttachment = await this.createDungeonImage(updatedDungeonInfo);
      const rankColor = await this.getRankColor(updatedDungeonInfo.rank);

      // Create updated embed
      const updatedEmbed = new EmbedBuilder()
        .setTitle(dungeonData.title)
        .setColor(rankColor)
        .setImage('attachment://dungeon-info.png')
        .setFooter({ text: 'Beta Access | Public Soon!' });

      // Edit the message with updated image
      await message.edit({
        embeds: [updatedEmbed],
        files: [newAttachment]
      });

      console.log(`[DungeonAlert] ✅ Updated dungeon message ${dungeonData.messageId} with ${roomCount} rooms`);

      // Update stored data
      dungeonData.dungeonInfo.rooms = roomCount;

    } catch (error) {
      console.error('[DungeonAlert] Error updating dungeon message with rooms:', error);
    }
  }

  // Check if a cycle is recent (within the last 15 minutes)
  isRecentCycle(messageCycle, currentCycle) {
    console.log(`[DungeonAlert] 🔍 Checking if cycle ${messageCycle} is recent compared to ${currentCycle}`);

    if (messageCycle === currentCycle) {
      console.log(`[DungeonAlert] ✅ Exact cycle match`);
      return true;
    }

    // Parse cycle times
    const [msgHour, msgMinute] = messageCycle.split(':').map(Number);
    const [curHour, curMinute] = currentCycle.split(':').map(Number);

    // Convert to minutes since midnight
    const msgTotalMinutes = msgHour * 60 + msgMinute;
    const curTotalMinutes = curHour * 60 + curMinute;

    console.log(`[DungeonAlert] 🔍 Message cycle: ${msgTotalMinutes} minutes, Current cycle: ${curTotalMinutes} minutes`);

    // Check if message cycle is within 15 minutes of current cycle
    // This allows for room messages to be associated with dungeons from the same or previous cycle
    const timeDiff = Math.abs(curTotalMinutes - msgTotalMinutes);

    // Handle day boundary (e.g., 23:45 vs 00:00)
    const timeDiffAcrossMidnight = Math.abs((curTotalMinutes + 1440) - msgTotalMinutes);
    const timeDiffAcrossMidnight2 = Math.abs(curTotalMinutes - (msgTotalMinutes + 1440));

    const isRecent = timeDiff <= 15 || timeDiffAcrossMidnight <= 15 || timeDiffAcrossMidnight2 <= 15;
    console.log(`[DungeonAlert] 🔍 Time diff: ${timeDiff}, Across midnight 1: ${timeDiffAcrossMidnight}, Across midnight 2: ${timeDiffAcrossMidnight2}`);
    console.log(`[DungeonAlert] ${isRecent ? '✅' : '❌'} Cycle is ${isRecent ? 'recent' : 'not recent'}`);

    return isRecent;
  }

  // Get current dungeon cycle based on time (xx:15, xx:30, xx:45, xx:00)
  getCurrentDungeonCycle() {
    const now = new Date();
    const kolkataTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
    const hour = kolkataTime.getHours();
    const minute = kolkataTime.getMinutes();

    // Determine which 15-minute cycle we're in
    let cycleMinute;
    if (minute >= 0 && minute < 15) cycleMinute = 0;
    else if (minute >= 15 && minute < 30) cycleMinute = 15;
    else if (minute >= 30 && minute < 45) cycleMinute = 30;
    else cycleMinute = 45;

    return `${hour}:${cycleMinute.toString().padStart(2, '0')}`;
  }

  // Clean up old dungeon messages (updated for array storage)
  cleanupOldDungeonMessages() {
    const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);

    for (const [serverId, messageArray] of this.recentDungeonMessages.entries()) {
      if (Array.isArray(messageArray)) {
        // Filter out old messages
        const recentMessages = messageArray.filter(msg => msg.timestamp >= thirtyMinutesAgo);

        if (recentMessages.length === 0) {
          this.recentDungeonMessages.delete(serverId);
          console.log(`[DungeonAlert] Cleaned up all old messages for server ${serverId}`);
        } else if (recentMessages.length < messageArray.length) {
          this.recentDungeonMessages.set(serverId, recentMessages);
          console.log(`[DungeonAlert] Cleaned up ${messageArray.length - recentMessages.length} old messages for server ${serverId}`);
        }
      }
    }
  }

  // Updated debug method for array storage
  getStoredDungeonMessages() {
    console.log(`[DungeonAlert] 📊 Currently stored dungeon messages:`);
    if (this.recentDungeonMessages.size === 0) {
      console.log(`[DungeonAlert] 📭 No stored messages`);
      return;
    }

    for (const [serverId, messageArray] of this.recentDungeonMessages.entries()) {
      console.log(`[DungeonAlert] 📝 Server ${serverId}: ${Array.isArray(messageArray) ? messageArray.length : 1} messages`);

      if (Array.isArray(messageArray)) {
        messageArray.forEach((messageData, index) => {
          const ageMinutes = Math.floor((Date.now() - messageData.timestamp) / (1000 * 60));
          console.log(`[DungeonAlert]   ${index + 1}. Message ID: ${messageData.messageId}`);
          console.log(`[DungeonAlert]      Channel: ${messageData.channelId}`);
          console.log(`[DungeonAlert]      Age: ${ageMinutes} minutes`);
          console.log(`[DungeonAlert]      Cycle: ${messageData.cycle}`);
          console.log(`[DungeonAlert]      Title: ${messageData.title}`);
          console.log(`[DungeonAlert]      Timestamp: ${messageData.timestamp}`);
        });
      } else {
        // Handle old single message format
        const ageMinutes = Math.floor((Date.now() - messageArray.timestamp) / (1000 * 60));
        console.log(`[DungeonAlert]   1. Message ID: ${messageArray.messageId} (old format)`);
        console.log(`[DungeonAlert]      Channel: ${messageArray.channelId}`);
        console.log(`[DungeonAlert]      Age: ${ageMinutes} minutes`);
        console.log(`[DungeonAlert]      Title: ${messageArray.title}`);
        console.log(`[DungeonAlert]      Timestamp: ${messageArray.timestamp}`);
      }
    }
  }
  // Extract room count from message content
  extractRoomCount(content) {
    if (!content) return null;

    console.log(`[DungeonAlert] 🔍 Extracting room count from: "${content}"`);

    // Look for patterns like "# 6 ROOMS #", "# 8 ROOMS", "8 rooms", etc.
    const patterns = [
      /#\s*(\d+)\s*rooms?\s*#/i,  // # 6 ROOMS #
      /#\s*(\d+)\s*rooms?/i,      // # 8 ROOMS
      /(\d+)\s*rooms?\s*#/i,      // 6 ROOMS #
      /(?:#\s*)?(\d+)\s*rooms?/i, // 8 ROOMS or # 8 ROOMS
      /rooms?\s*:?\s*(\d+)/i,     // rooms: 4
      /(\d+)\s*room/i             // 4 room
    ];

    for (let i = 0; i < patterns.length; i++) {
      const pattern = patterns[i];
      const match = content.match(pattern);
      console.log(`[DungeonAlert] 🔍 Pattern ${i + 1} (${pattern}): ${match ? `MATCH -> ${match[1]}` : 'NO MATCH'}`);

      if (match) {
        const roomCount = parseInt(match[1]);
        if (roomCount > 0 && roomCount <= 20) { // Reasonable room count range
          console.log(`[DungeonAlert] ✅ Extracted room count: ${roomCount} from message: "${content}"`);
          return roomCount;
        } else {
          console.log(`[DungeonAlert] ❌ Room count ${roomCount} is out of range (1-20)`);
        }
      }
    }

    console.log(`[DungeonAlert] ❌ No valid room count found in message: "${content}"`);
    return null;
  }

  // Update the dungeon message with room information
  async updateDungeonMessageWithRooms(dungeonData, roomCount) {
    try {
      const channel = await this.client.channels.fetch(dungeonData.channelId);
      if (!channel) {
        console.error('[DungeonAlert] Channel not found for room update');
        return;
      }

      const message = await channel.messages.fetch(dungeonData.messageId);
      if (!message) {
        console.error('[DungeonAlert] Message not found for room update');
        return;
      }

      // Create updated dungeon info with room count
      const updatedDungeonInfo = {
        ...dungeonData.dungeonInfo,
        rooms: roomCount
      };

      // Create new image with room information
      const newAttachment = await this.createDungeonImage(updatedDungeonInfo);
      const rankColor = await this.getRankColor(updatedDungeonInfo.rank);

      // Create updated embed
      const updatedEmbed = new EmbedBuilder()
        .setTitle(dungeonData.title)
        .setColor(rankColor)
        .setImage('attachment://dungeon-info.png')
        .setFooter({ text: 'Beta Access | Public Soon!' });

      // Edit the message with updated image
      await message.edit({
        embeds: [updatedEmbed],
        files: [newAttachment]
      });

      console.log(`[DungeonAlert] ✅ Updated dungeon message ${dungeonData.messageId} with ${roomCount} rooms`);

      // Update stored data
      dungeonData.dungeonInfo.rooms = roomCount;

    } catch (error) {
      console.error('[DungeonAlert] Error updating dungeon message with rooms:', error);
    }
  }

  // Get current dungeon cycle based on time (xx:15, xx:30, xx:45, xx:00)
  getCurrentDungeonCycle() {
    const now = new Date();
    const kolkataTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
    const hour = kolkataTime.getHours();
    const minute = kolkataTime.getMinutes();

    // Determine which 15-minute cycle we're in
    let cycleMinute;
    if (minute >= 0 && minute < 15) cycleMinute = 0;
    else if (minute >= 15 && minute < 30) cycleMinute = 15;
    else if (minute >= 30 && minute < 45) cycleMinute = 30;
    else cycleMinute = 45;

    return `${hour}:${cycleMinute.toString().padStart(2, '0')}`;
  }
}

module.exports = DungeonAlert;