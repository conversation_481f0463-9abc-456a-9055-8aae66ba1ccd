const https = require('https');
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { CronJob } = require('cron');
const FormData = require('form-data');
const config = require('../../config/config.js');
const serverConfigs = require('../../config/serverConfigs.js');

// Configuration
const MONITOR_CHANNEL_ID = '1381771149255512135';
const USER_TOKEN = config.tokenDungeon;
const SHARED_FOLDER = path.join(__dirname, 'worldboss_alerts');
const OCR_API_KEY = config.ImageConfig.OCR_API_KEY;

// Create shared folder if it doesn't exist
if (!fs.existsSync(SHARED_FOLDER)) {
    fs.mkdirSync(SHARED_FOLDER, { recursive: true });
    console.log(`[WorldBoss2 Monitor] Created world boss alerts folder: ${SHARED_FOLDER}`);
}

// Store last processed message ID to avoid duplicates
let lastProcessedMessageId = null;

// Monitoring state
let isMonitoringActive = false;
let monitoringCronJobs = [];

// Boss name mapping for consistency
const BOSS_NAME_MAPPING = {
    'BOTO': 'Boto',
    'BARUTO': 'Baruto',
    'BAIZEN': 'Baizen',
    'FORCE': 'Force',
    'BEGETA': 'Begeta',
    'ALIEN': 'Alien',
    'GON': 'Gon',
    'DON': 'Don',
    'PROTAGONIST': 'Protagonist',
    'BEACH ZIRU': 'Beach Ziru',
    'BEACH_ZIRU': 'Beach Ziru',
    'BEACH VERMILLION': 'Beach Vermillion',
    'BEACH_VERMILLION': 'Beach Vermillion'
};

// Function to check if a timestamp is within the current monitoring window
function isWithinMonitoringWindow(messageTimestamp) {
    const now = new Date();
    const messageTime = new Date(messageTimestamp);

    // Convert to Asia/Kolkata timezone
    const nowKolkata = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
    const messageKolkata = new Date(messageTime.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));

    const currentMinute = nowKolkata.getMinutes();

    // Check if we're currently in a monitoring window
    const isInWindow10 = (currentMinute >= 10 && currentMinute <= 11);
    const isInWindow50 = (currentMinute >= 50 && currentMinute <= 51);

    if (!isInWindow10 && !isInWindow50) {
        return false; // Not currently in a monitoring window
    }

    // Calculate time difference in minutes
    const timeDiffMs = nowKolkata.getTime() - messageKolkata.getTime();
    const timeDiffMinutes = Math.floor(timeDiffMs / (1000 * 60));

    // Process messages from the last 15 minutes
    if (timeDiffMinutes <= 15) {
        console.log(`[WorldBoss2 Monitor] Message from ${messageTimestamp} is ${timeDiffMinutes} minutes old - processing`);
        return true;
    } else {
        console.log(`[WorldBoss2 Monitor] Skipping message from ${messageTimestamp} - ${timeDiffMinutes} minutes old (too old)`);
        return false;
    }
}

// Function to fetch latest messages from Discord channel (only within monitoring window)
async function fetchLatestMessages() {
    try {
        const response = await axios.get(`https://discord.com/api/v10/channels/${MONITOR_CHANNEL_ID}/messages`, {
            headers: {
                'Authorization': USER_TOKEN,
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            params: {
                limit: 10 // Get last 10 messages
            }
        });

        // Filter messages to only include those within the current monitoring window
        const filteredMessages = response.data.filter(message => {
            const isWithinWindow = isWithinMonitoringWindow(message.timestamp);
            if (!isWithinWindow) {
                console.log(`[WorldBoss2 Monitor] Skipping message from ${message.timestamp} - outside monitoring window`);
            }
            return isWithinWindow;
        });

        console.log(`[WorldBoss2 Monitor] Fetched ${response.data.length} messages, ${filteredMessages.length} within monitoring window`);
        return filteredMessages;

    } catch (error) {
        console.error('[WorldBoss2 Monitor] Error fetching messages:', error.response?.status, error.response?.statusText);
        return null;
    }
}

// Function to download image from Discord
async function downloadImage(imageUrl) {
    try {
        const response = await axios.get(imageUrl, {
            responseType: 'arraybuffer',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        return Buffer.from(response.data);
    } catch (error) {
        console.error('[WorldBoss2 Monitor] Error downloading image:', error.message);
        return null;
    }
}

// Function to upload image to ImgBB and get public URL
async function uploadToImgBB(imageBuffer) {
    try {
        const base64Image = imageBuffer.toString('base64');

        const formData = new FormData();
        formData.append('key', 'edaf682b1adbf9eaa0e1a6026a338b4e');
        formData.append('image', base64Image);
        formData.append('name', 'worldboss_' + Date.now());

        const response = await axios.post('https://api.imgbb.com/1/upload', formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            timeout: 30000
        });

        if (response.data && response.data.success && response.data.data) {
            const publicUrl = response.data.data.url;
            console.log(`[WorldBoss2 Monitor] Successfully uploaded to ImgBB: ${publicUrl}`);
            return publicUrl;
        } else {
            console.error('[WorldBoss2 Monitor] ImgBB upload failed:', response.data);
            return null;
        }
    } catch (error) {
        console.error('[WorldBoss2 Monitor] Error uploading to ImgBB:', error.response?.data || error.message);
        return null;
    }
}

// Multiple OCR.space API keys for redundancy
const OCR_API_KEYS = [
    'K82535073388957', // Free tier key 1
    'K81921594688957',
    'K84480225188957',
    'K82363173688957',
    'K89818139288957',
    'K81659269188957'
];

// Function to perform OCR using OCR.space API with multiple keys and retry logic
async function performOCRWithOCRSpace(imageUrl, keyIndex = 0, retryCount = 0) {
    const maxRetries = 2; // Retry each key up to 2 times
    const retryDelay = 3000; // 3 seconds between retries

    if (keyIndex >= OCR_API_KEYS.length) {
        console.error('[WorldBoss2 Monitor] All OCR.space API keys exhausted');
        return null;
    }

    const currentKey = OCR_API_KEYS[keyIndex];

    try {
        console.log(`[WorldBoss2 Monitor] Using OCR.space (key ${keyIndex + 1}/${OCR_API_KEYS.length}, attempt ${retryCount + 1}/${maxRetries + 1}) for: ${imageUrl}`);

        const formData = new FormData();
        formData.append('url', imageUrl);
        formData.append('language', 'eng');
        formData.append('isOverlayRequired', 'false');
        formData.append('detectOrientation', 'false');
        formData.append('scale', 'true');
        formData.append('OCREngine', '2'); // Use engine 2 for better accuracy

        const response = await axios.post('https://api.ocr.space/parse/image', formData, {
            headers: {
                'apikey': currentKey,
                ...formData.getHeaders()
            },
            timeout: 20000 // 20 second timeout
        });

        console.log(`[WorldBoss2 Monitor] OCR.space Response Status: ${response.status} (key ${keyIndex + 1})`);

        if (response.data && response.data.ParsedResults && response.data.ParsedResults.length > 0) {
            const extractedText = response.data.ParsedResults[0].ParsedText.trim();
            if (extractedText.length > 0) {
                console.log(`[WorldBoss2 Monitor] ✅ Successfully extracted text with OCR.space (key ${keyIndex + 1}):`, extractedText);
                return extractedText;
            }
        }

        // Check for specific OCR.space errors
        if (response.data && response.data.OCRExitCode !== 1) {
            console.error(`[WorldBoss2 Monitor] OCR.space processing error (key ${keyIndex + 1}):`, response.data.ErrorMessage);
        } else {
            console.error(`[WorldBoss2 Monitor] OCR.space returned no text (key ${keyIndex + 1})`);
        }

        // Try next key if current one failed
        return await performOCRWithOCRSpace(imageUrl, keyIndex + 1, 0);

    } catch (error) {
        const isTimeoutError = error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED' || error.message.includes('timeout');
        const isNetworkError = error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED';
        const isRateLimited = error.response?.status === 429;

        console.error(`[WorldBoss2 Monitor] OCR.space error (key ${keyIndex + 1}, attempt ${retryCount + 1}):`, error.response?.data || error.message);

        // Retry with same key if it's a timeout/network error and we haven't exceeded retries
        if ((isTimeoutError || isNetworkError) && retryCount < maxRetries) {
            console.warn(`[WorldBoss2 Monitor] ⏳ Retrying OCR.space (key ${keyIndex + 1}) in ${retryDelay / 1000} seconds...`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return await performOCRWithOCRSpace(imageUrl, keyIndex, retryCount + 1);
        }

        // If rate limited or max retries reached, try next key
        if (isRateLimited) {
            console.warn(`[WorldBoss2 Monitor] 🚫 Rate limited on key ${keyIndex + 1}, trying next key...`);
        } else {
            console.warn(`[WorldBoss2 Monitor] ❌ Max retries reached for key ${keyIndex + 1}, trying next key...`);
        }

        return await performOCRWithOCRSpace(imageUrl, keyIndex + 1, 0);
    }
}

// Function to perform image-to-text extraction with multiple fallbacks
async function performImageToText(imageUrl, retryCount = 0) {
    const maxRetries = 2; // Reduced retries since we have fallbacks
    const retryDelay = 2000;

    // Try OCR.space first (more reliable)
    console.log(`[WorldBoss2 Monitor] Attempting OCR with OCR.space...`);
    let ocrText = await performOCRWithOCRSpace(imageUrl);

    if (ocrText) {
        return ocrText;
    }

    // Fallback to Puter AI if OCR.space fails
    try {
        console.log(`[WorldBoss2 Monitor] OCR.space failed, trying Puter AI for image-to-text extraction: ${imageUrl}`);

        const response = await axios.post('https://api.puter.com/ai/img2txt', {
            image_url: imageUrl
        }, {
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Origin': 'https://puter.com',
                'Referer': 'https://puter.com/'
            },
            timeout: 15000 // Reduced timeout
        });

        console.log('[WorldBoss2 Monitor] Puter AI Response Status:', response.status);

        if (response.data && response.data.text) {
            const extractedText = response.data.text.trim();
            if (extractedText.length > 0) {
                console.log('[WorldBoss2 Monitor] Successfully extracted text with Puter AI:', extractedText);
                return extractedText;
            }
        }

        console.error('[WorldBoss2 Monitor] Puter AI returned no text data');
        return null;

    } catch (error) {
        const isTimeoutError = error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED' || error.message.includes('timeout');

        if (isTimeoutError && retryCount < maxRetries) {
            console.warn(`[WorldBoss2 Monitor] Puter AI timeout (attempt ${retryCount + 1}/${maxRetries + 1}). Retrying in ${retryDelay / 1000} seconds...`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return await performImageToText(imageUrl, retryCount + 1);
        }

        console.error(`[WorldBoss2 Monitor] All OCR methods failed. Final error:`, error.response?.data || error.message);
        return null;
    }
}

// Function to parse OCR text and extract world boss information
function parseWorldBossText(ocrText) {
    try {
        console.log('[WorldBoss2 Monitor] Raw OCR Text:', ocrText);

        const lines = ocrText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
        const worldBosses = {};

        // Try different parsing strategies

        // Strategy 1: Look for "World X: BossName" or "WX: BossName" pattern
        for (const line of lines) {
            const worldBossMatch = line.match(/(?:World\s*(\d+)|W(\d+))\s*[:]\s*(.+)/i);
            if (worldBossMatch) {
                const worldNum = parseInt(worldBossMatch[1] || worldBossMatch[2]);
                const bossName = worldBossMatch[3].trim();

                if ((worldNum === 1 || worldNum === 2) && bossName.length > 0) {
                    const normalizedBossName = normalizeBossName(bossName);
                    if (normalizedBossName) {
                        worldBosses[worldNum] = {
                            boss: normalizedBossName,
                            is_active: true,
                            world: worldNum
                        };
                        console.log(`[WorldBoss2 Monitor] Found World ${worldNum}: ${normalizedBossName} (Pattern match)`);
                    }
                }
            }
        }

        // Strategy 2: Sequential parsing (World X followed by boss name)
        if (Object.keys(worldBosses).length === 0) {
            let currentWorld = null;

            for (let i = 0; i < lines.length; i++) {
                const line = lines[i].toUpperCase();

                // Check for World indicators
                if (line.includes('WORLD 1') || line === 'WORLD1' || line === 'W1') {
                    currentWorld = 1;
                    continue;
                } else if (line.includes('WORLD 2') || line === 'WORLD2' || line === 'W2') {
                    currentWorld = 2;
                    continue;
                }

                // If we have a current world, check if this line is a boss name
                if (currentWorld && line.length > 2) {
                    // Skip common non-boss words
                    const skipWords = ['WORLD', 'BOSS', 'ALERT', 'UPDATE', 'STATUS', 'ACTIVE', 'INACTIVE', 'SPAWN', 'TIME'];
                    if (skipWords.some(word => line.includes(word))) {
                        continue;
                    }

                    const normalizedBossName = normalizeBossName(line);
                    if (normalizedBossName) {
                        worldBosses[currentWorld] = {
                            boss: normalizedBossName,
                            is_active: true,
                            world: currentWorld
                        };

                        console.log(`[WorldBoss2 Monitor] Found World ${currentWorld}: ${normalizedBossName} (Sequential parsing)`);
                        currentWorld = null; // Reset to avoid duplicate assignments
                    }
                }
            }
        }

        // Validate that we found at least one world boss
        if (Object.keys(worldBosses).length === 0) {
            console.log('[WorldBoss2 Monitor] No valid world bosses found in OCR text');
            console.log('[WorldBoss2 Monitor] Available lines:', lines);
            return null;
        }

        return {
            worldBosses: worldBosses,
            timestamp: new Date().toISOString(),
            lastUpdate: new Date().toISOString(),
            nextSpawnCountdown: null,
            source: 'discord_ocr'
        };

    } catch (error) {
        console.error('[WorldBoss2 Monitor] Error parsing world boss text:', error);
        return null;
    }
}

// Helper function to normalize boss names
function normalizeBossName(rawName) {
    const cleanName = rawName.trim().toUpperCase();

    // Check exact matches first
    if (BOSS_NAME_MAPPING[cleanName]) {
        return BOSS_NAME_MAPPING[cleanName];
    }

    // Check partial matches
    for (const [key, value] of Object.entries(BOSS_NAME_MAPPING)) {
        if (cleanName.includes(key) || key.includes(cleanName)) {
            return value;
        }
    }

    // If no mapping found but looks like a boss name (not too short, not just numbers)
    if (cleanName.length >= 3 && !/^\d+$/.test(cleanName) && !/^[^A-Z]*$/.test(cleanName)) {
        // Capitalize first letter of each word
        return cleanName.toLowerCase().split(' ').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    return null;
}

// Function to check if world boss state has changed
function hasStateChanged(newData) {
    if (!lastKnownState) return true;

    // Compare world 1 and world 2 boss states
    for (const worldNum of [1, 2]) {
        const oldBoss = lastKnownState.worldBosses[worldNum];
        const newBoss = newData.worldBosses[worldNum];

        // If one exists and the other doesn't, it's a change
        if ((!oldBoss && newBoss) || (oldBoss && !newBoss)) {
            return true;
        }

        // If both exist, check if boss name changed
        if (oldBoss && newBoss) {
            if (oldBoss.boss !== newBoss.boss) {
                return true;
            }
        }
    }

    return false;
}

// Store last known state to detect changes
let lastKnownState = null;

// Process world boss data and create alerts (same as original)
function processWorldBossData(worldBossData) {
    console.log('[WorldBoss2 Monitor] Processing world boss data!', worldBossData);

    // Get all enabled servers for world boss alerts
    const enabledServers = serverConfigs.getEnabledWorldBossServers();

    // Create ping roles for each world boss
    const worldBossPings = {};
    for (const [worldNum, bossInfo] of Object.entries(worldBossData.worldBosses)) {
        worldBossPings[worldNum] = [];

        // Add pings for each enabled server
        enabledServers.forEach(server => {
            const worldBossRoles = server.config.worldBossRoles || {};

            // Get general world boss ping role
            if (worldBossRoles.general) {
                worldBossPings[worldNum].push(`<@&${worldBossRoles.general}>`);
            }

            // Get boss-specific ping role
            const bossKey = bossInfo.boss.toLowerCase().replace(/\s+/g, '_');
            if (worldBossRoles[bossKey]) {
                worldBossPings[worldNum].push(`<@&${worldBossRoles[bossKey]}>`);
            }
        });
    }

    // Create standardized format for world boss data
    const bossData = {
        title: 'WORLD BOSS ALERT',
        worldBosses: worldBossData.worldBosses,
        worldBossPings: worldBossPings,
        timestamp: worldBossData.timestamp,
        lastUpdate: worldBossData.lastUpdate,
        nextSpawnCountdown: worldBossData.nextSpawnCountdown,
        type: 'world_boss',
        source: 'discord_ocr',
        enabledServers: enabledServers
    };

    // Generate unique filename
    const filename = `worldboss_${Date.now()}.json`;
    const filePath = path.join(SHARED_FOLDER, filename);

    // Write data to file
    fs.writeFileSync(filePath, JSON.stringify(bossData, null, 2));
    console.log(`[WorldBoss2 Monitor] Saved world boss alert to ${filename} for ${enabledServers.length} servers`);

    // Update last known state
    lastKnownState = worldBossData;
}

// Main monitoring function
async function monitorDiscordChannel() {
    try {
        // console.log('[WorldBoss2 Monitor] Checking for new world boss images...');

        const messages = await fetchLatestMessages();
        if (!messages || messages.length === 0) {
            return;
        }

        // Find the latest message with an image
        for (const message of messages) {
            // Skip if we already processed this message
            if (lastProcessedMessageId && message.id === lastProcessedMessageId) {
                break;
            }

            // Check if message has attachments (images)
            if (message.attachments && message.attachments.length > 0) {
                console.log(`[WorldBoss2 Monitor] Message ${message.id} has ${message.attachments.length} attachments`);
                for (const attachment of message.attachments) {
                    console.log(`[WorldBoss2 Monitor] Attachment: ${attachment.filename}, type: ${attachment.content_type}, url: ${attachment.url}`);
                    // Check if attachment is an image
                    if (attachment.content_type && attachment.content_type.startsWith('image/')) {
                        console.log(`[WorldBoss2 Monitor] Found new image: ${attachment.filename}`);

                        // Download image from Discord
                        console.log(`[WorldBoss2 Monitor] Downloading image from Discord: ${attachment.url}`);
                        const imageBuffer = await downloadImage(attachment.url);

                        if (!imageBuffer) {
                            console.log('[WorldBoss2 Monitor] Failed to download image from Discord');
                            continue;
                        }

                        // Upload to ImgBB to get public URL
                        console.log(`[WorldBoss2 Monitor] Uploading image to ImgBB...`);
                        const publicImageUrl = await uploadToImgBB(imageBuffer);

                        if (!publicImageUrl) {
                            console.log('[WorldBoss2 Monitor] Failed to upload image to ImgBB');
                            continue;
                        }

                        // Use the public ImgBB URL with Puter AI
                        console.log(`[WorldBoss2 Monitor] Starting OCR processing for: ${publicImageUrl}`);
                        const ocrText = await performImageToText(publicImageUrl);

                        if (ocrText) {
                            console.log(`[WorldBoss2 Monitor] OCR successful, extracted text: "${ocrText}"`);
                            // Parse world boss data
                            const worldBossData = parseWorldBossText(ocrText);
                            if (worldBossData && hasStateChanged(worldBossData)) {
                                console.log('[WorldBoss2 Monitor] World boss state changed, creating alert');
                                processWorldBossData(worldBossData);
                            } else if (worldBossData) {
                                console.log('[WorldBoss2 Monitor] No changes in world boss state');
                                lastKnownState = worldBossData;
                            } else {
                                console.log('[WorldBoss2 Monitor] Failed to parse world boss data from OCR text');
                            }
                        } else {
                            console.log('[WorldBoss2 Monitor] OCR failed - no text extracted');
                        }

                        // Update last processed message ID
                        lastProcessedMessageId = message.id;
                        return; // Process only the latest image
                    } else {
                        console.log(`[WorldBoss2 Monitor] Skipping non-image attachment: ${attachment.content_type}`);
                    }
                }
            } else {
                console.log(`[WorldBoss2 Monitor] Message ${message.id} has no attachments`);
            }
        }

        // If no new images found, update last processed message ID to the latest message
        if (messages.length > 0 && !lastProcessedMessageId) {
            lastProcessedMessageId = messages[0].id;
        }

    } catch (error) {
        console.error('[WorldBoss2 Monitor] Error monitoring Discord channel:', error);
    }
}

// Function to start monitoring (called by cron)
async function startMonitoring() {
    if (isMonitoringActive) {
        console.log('[WorldBoss2 Monitor] Monitoring already active, skipping...');
        return;
    }

    // Reset last processed message ID when starting a new monitoring window
    // This ensures we don't carry over state from previous windows
    lastProcessedMessageId = null;
    console.log('[WorldBoss2 Monitor] 🔄 Reset message tracking for new monitoring window');

    isMonitoringActive = true;
    console.log('[WorldBoss2 Monitor] 🟢 Starting world boss image monitoring...');

    // Initial check
    await monitorDiscordChannel();

    // Set up interval to check every 10 seconds during active period
    const monitoringInterval = setInterval(async () => {
        if (isMonitoringActive) {
            await monitorDiscordChannel();
        } else {
            clearInterval(monitoringInterval);
        }
    }, 10000); // Check every 10 seconds during active period
}

// Function to stop monitoring (called by cron)
async function stopMonitoring() {
    if (!isMonitoringActive) {
        return;
    }

    isMonitoringActive = false;
    console.log('[WorldBoss2 Monitor] 🔴 Stopping world boss image monitoring...');
}

// Setup cron jobs for time-based monitoring
function setupCronJobs() {
    const timezone = 'Asia/Kolkata';

    // Start monitoring at xx:10 (10th minute of every hour)
    const startCron1 = new CronJob(
        '0 10 * * * *',  // At minute 10 of every hour
        () => {
            console.log('[WorldBoss2 Monitor] ⏰ Cron triggered: Starting monitoring at xx:10');
            startMonitoring();
        },
        null,
        true,
        timezone
    );

    // Stop monitoring at xx:11 (11th minute of every hour)
    const stopCron1 = new CronJob(
        '0 11 * * * *',  // At minute 11 of every hour
        () => {
            console.log('[WorldBoss2 Monitor] ⏰ Cron triggered: Stopping monitoring at xx:11');
            stopMonitoring();
        },
        null,
        true,
        timezone
    );

    // Start monitoring at xx:50 (50th minute of every hour)
    const startCron2 = new CronJob(
        '0 50 * * * *',  // At minute 50 of every hour
        () => {
            console.log('[WorldBoss2 Monitor] ⏰ Cron triggered: Starting monitoring at xx:50');
            startMonitoring();
        },
        null,
        true,
        timezone
    );

    // Stop monitoring at xx:51 (51st minute of every hour)
    const stopCron2 = new CronJob(
        '0 51 * * * *',  // At minute 51 of every hour
        () => {
            console.log('[WorldBoss2 Monitor] ⏰ Cron triggered: Stopping monitoring at xx:51');
            stopMonitoring();
        },
        null,
        true,
        timezone
    );

    monitoringCronJobs = [startCron1, stopCron1, startCron2, stopCron2];

    console.log('[WorldBoss2 Monitor] ⏰ Cron jobs scheduled:');
    console.log('[WorldBoss2 Monitor]   - Start monitoring: xx:10 and xx:50 (Asia/Kolkata)');
    console.log('[WorldBoss2 Monitor]   - Stop monitoring: xx:11 and xx:51 (Asia/Kolkata)');
}

// Main function
async function main() {
    try {
        console.log('[WorldBoss2 Monitor] Starting world boss monitor with Discord OCR...');

        // Validate configuration
        if (!USER_TOKEN) {
            throw new Error('tokenDungeon not configured in config.js');
        }

        console.log('[WorldBoss2 Monitor] Using Puter AI for image-to-text extraction (no API key required)');

        // Setup cron-based monitoring
        setupCronJobs();

        console.log('[WorldBoss2 Monitor] ✅ World boss monitor initialized with time-based monitoring');
        console.log('[WorldBoss2 Monitor] 📅 Monitoring windows: xx:10-xx:11 and xx:50-xx:51 (Asia/Kolkata)');

    } catch (error) {
        console.error('[WorldBoss2 Monitor] Failed to start world boss monitor:', error);
    }
}

// Start the program
main();
